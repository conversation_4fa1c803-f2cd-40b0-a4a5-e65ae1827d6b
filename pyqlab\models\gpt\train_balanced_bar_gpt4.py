"""
平衡训练脚本 - 解决token分布不均衡问题
"""

import os
import sys
import torch
import argparse
import numpy as np
from pathlib import Path
from collections import Counter
import pytorch_lightning as pl
from pytorch_lightning.callbacks import ModelCheckpoint, EarlyStopping, LearningRateMonitor

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from pyqlab.models.gpt.bar_gpt4 import BarGpt4
from pyqlab.models.gpt.token_balance_utils import TokenBalancer, create_balanced_dataloader, analyze_model_predictions
from pyqlab.data.dataset.utils import get_vocab
from pyqlab.data.dataset.dataset_bar import BarDataset


class BalancedBarGpt4Trainer(pl.LightningModule):
    """平衡训练的BarGpt4模型"""
    
    def __init__(self, model_config, training_config):
        super().__init__()
        self.save_hyperparameters()
        
        # 创建模型
        self.model = BarGpt4(**model_config)
        
        # 训练配置
        self.learning_rate = training_config['learning_rate']
        self.weight_decay = training_config['weight_decay']
        self.use_class_weights = training_config.get('use_class_weights', True)
        self.balance_method = training_config.get('balance_method', 'focal_loss')
        
        # 类别权重（将在setup中设置）
        self.class_weights = None
        
        # 记录训练统计
        self.train_predictions = []
        self.val_predictions = []
    
    def setup(self, stage=None):
        """设置阶段 - 计算类别权重"""
        if stage == 'fit' and self.use_class_weights:
            # 从训练数据计算类别权重
            train_dataloader = self.trainer.datamodule.train_dataloader()
            
            # 收集所有训练token
            all_tokens = []
            for batch in train_dataloader:
                if isinstance(batch, dict):
                    targets = batch['targets']
                else:
                    targets = batch[-1]  # 假设targets是最后一个
                
                # 过滤有效token（非-1）
                valid_tokens = targets[targets != -1]
                all_tokens.extend(valid_tokens.tolist())
            
            # 计算类别权重
            balancer = TokenBalancer(self.model.vocab_size)
            token_sequences = [all_tokens]  # 包装成序列格式
            balancer.analyze_token_distribution(token_sequences, save_plot=True, 
                                              plot_path="train_token_distribution.png")
            
            if self.balance_method == 'weighted_loss':
                self.class_weights = balancer.compute_class_weights(
                    method='sqrt_inverse_freq', smooth_factor=1.0
                ).to(self.device)
                print(f"已计算类别权重，权重范围: [{self.class_weights.min():.4f}, {self.class_weights.max():.4f}]")
    
    def training_step(self, batch, batch_idx):
        """训练步骤"""
        if isinstance(batch, dict):
            code = batch['code']
            x = batch['x']
            x_mark = batch['x_mark']
            targets = batch['targets']
        else:
            code, x, x_mark, targets = batch
        
        # 前向传播
        logits, loss = self.model(code, x, x_mark, targets, 
                                class_weights=self.class_weights if self.use_class_weights else None)
        
        # 记录预测用于分析
        if batch_idx % 100 == 0:  # 每100个batch记录一次
            with torch.no_grad():
                predictions = logits.argmax(dim=-1)
                self.train_predictions.extend(predictions[:, -1].cpu().tolist())
        
        # 记录损失
        self.log('train_loss', loss, on_step=True, on_epoch=True, prog_bar=True)
        
        return loss
    
    def validation_step(self, batch, batch_idx):
        """验证步骤"""
        if isinstance(batch, dict):
            code = batch['code']
            x = batch['x']
            x_mark = batch['x_mark']
            targets = batch['targets']
        else:
            code, x, x_mark, targets = batch
        
        # 前向传播
        logits, loss = self.model(code, x, x_mark, targets,
                                class_weights=self.class_weights if self.use_class_weights else None)
        
        # 记录预测
        with torch.no_grad():
            predictions = logits.argmax(dim=-1)
            self.val_predictions.extend(predictions[:, -1].cpu().tolist())
        
        # 记录损失
        self.log('val_loss', loss, on_step=False, on_epoch=True, prog_bar=True)
        
        return loss
    
    def on_validation_epoch_end(self):
        """验证轮次结束时分析预测多样性"""
        if len(self.val_predictions) > 0:
            pred_counter = Counter(self.val_predictions)
            unique_preds = len(pred_counter)
            total_preds = len(self.val_predictions)
            diversity = unique_preds / min(total_preds, self.model.vocab_size)
            
            self.log('val_prediction_diversity', diversity, on_epoch=True)
            
            # 检查是否存在过度集中的预测
            most_common = pred_counter.most_common(1)[0]
            max_freq = most_common[1] / total_preds
            
            if max_freq > 0.5:  # 如果某个token占比超过50%
                print(f"⚠️  警告: Token {most_common[0]} 在验证集中占比 {max_freq:.2%}")
            
            # 清空记录
            self.val_predictions = []
    
    def configure_optimizers(self):
        """配置优化器"""
        optimizer = torch.optim.AdamW(
            self.parameters(),
            lr=self.learning_rate,
            weight_decay=self.weight_decay,
            betas=(0.9, 0.95)
        )
        
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
            optimizer,
            T_max=self.trainer.max_epochs,
            eta_min=self.learning_rate * 0.1
        )
        
        return {
            "optimizer": optimizer,
            "lr_scheduler": {
                "scheduler": scheduler,
                "interval": "epoch",
            }
        }


class BalancedBarDataModule(pl.LightningDataModule):
    """平衡的数据模块"""
    
    def __init__(self, data_config, balance_config):
        super().__init__()
        self.data_config = data_config
        self.balance_config = balance_config
        
        self.train_dataset = None
        self.val_dataset = None
        self.test_dataset = None
    
    def setup(self, stage=None):
        """设置数据集"""
        # 这里需要根据实际的数据加载逻辑来实现
        # 示例实现：
        if stage == 'fit' or stage is None:
            # 加载训练和验证数据
            # self.train_dataset = BarDataset(...)
            # self.val_dataset = BarDataset(...)
            pass
        
        if stage == 'test' or stage is None:
            # 加载测试数据
            # self.test_dataset = BarDataset(...)
            pass
    
    def train_dataloader(self):
        """训练数据加载器"""
        if self.train_dataset is None:
            raise ValueError("训练数据集未设置")
        
        # 根据平衡配置创建数据加载器
        balance_method = self.balance_config.get('method', 'none')
        
        if balance_method == 'weighted_sampling':
            return create_balanced_dataloader(
                self.train_dataset,
                tokenizer=None,  # 需要传入实际的tokenizer
                batch_size=self.data_config['batch_size'],
                balance_method='weighted_sampling'
            )
        else:
            return torch.utils.data.DataLoader(
                self.train_dataset,
                batch_size=self.data_config['batch_size'],
                shuffle=True,
                num_workers=4,
                pin_memory=True
            )
    
    def val_dataloader(self):
        """验证数据加载器"""
        if self.val_dataset is None:
            raise ValueError("验证数据集未设置")
        
        return torch.utils.data.DataLoader(
            self.val_dataset,
            batch_size=self.data_config['batch_size'],
            shuffle=False,
            num_workers=4,
            pin_memory=True
        )


def main():
    """主训练函数"""
    parser = argparse.ArgumentParser(description='平衡训练BarGpt4模型')
    
    # 模型参数
    parser.add_argument('--block_size', type=int, default=128, help='序列长度')
    parser.add_argument('--code_size', type=int, default=100, help='代码嵌入大小')
    parser.add_argument('--vocab_size', type=int, default=1024, help='词汇表大小')
    parser.add_argument('--n_layer', type=int, default=6, help='Transformer层数')
    parser.add_argument('--n_head', type=int, default=8, help='注意力头数')
    parser.add_argument('--d_model', type=int, default=512, help='嵌入维度')
    
    # 训练参数
    parser.add_argument('--learning_rate', type=float, default=3e-4, help='学习率')
    parser.add_argument('--weight_decay', type=float, default=0.1, help='权重衰减')
    parser.add_argument('--batch_size', type=int, default=32, help='批次大小')
    parser.add_argument('--max_epochs', type=int, default=100, help='最大训练轮数')
    
    # 平衡参数
    parser.add_argument('--use_class_weights', action='store_true', help='使用类别权重')
    parser.add_argument('--balance_method', type=str, default='focal_loss', 
                       choices=['focal_loss', 'weighted_loss', 'none'], help='平衡方法')
    parser.add_argument('--data_balance_method', type=str, default='none',
                       choices=['weighted_sampling', 'oversampling', 'none'], help='数据平衡方法')
    
    # 其他参数
    parser.add_argument('--save_dir', type=str, default='./checkpoints', help='模型保存目录')
    parser.add_argument('--gpus', type=int, default=1, help='GPU数量')
    
    args = parser.parse_args()
    
    # 创建保存目录
    os.makedirs(args.save_dir, exist_ok=True)
    
    # 模型配置
    model_config = {
        'block_size': args.block_size,
        'code_size': args.code_size,
        'vocab_size': args.vocab_size,
        'n_layer': args.n_layer,
        'n_head': args.n_head,
        'd_model': args.d_model,
        'time_encoding': 'timeF',
        'time_embed_type': 'periodic',
        'freq': 't',
        'pos_embed_type': 'rope',
        'dropout': 0.1
    }
    
    # 训练配置
    training_config = {
        'learning_rate': args.learning_rate,
        'weight_decay': args.weight_decay,
        'use_class_weights': args.use_class_weights,
        'balance_method': args.balance_method
    }
    
    # 数据配置
    data_config = {
        'batch_size': args.batch_size
    }
    
    # 平衡配置
    balance_config = {
        'method': args.data_balance_method
    }
    
    # 创建模型和数据模块
    model = BalancedBarGpt4Trainer(model_config, training_config)
    datamodule = BalancedBarDataModule(data_config, balance_config)
    
    # 设置回调
    callbacks = [
        ModelCheckpoint(
            dirpath=args.save_dir,
            filename='best-{epoch:02d}-{val_loss:.4f}',
            monitor='val_loss',
            mode='min',
            save_top_k=3
        ),
        EarlyStopping(
            monitor='val_loss',
            patience=10,
            mode='min'
        ),
        LearningRateMonitor(logging_interval='epoch')
    ]
    
    # 创建训练器
    trainer = pl.Trainer(
        max_epochs=args.max_epochs,
        gpus=args.gpus,
        callbacks=callbacks,
        precision=16,  # 使用混合精度训练
        gradient_clip_val=1.0,
        accumulate_grad_batches=1,
        log_every_n_steps=50
    )
    
    # 开始训练
    print("开始平衡训练...")
    trainer.fit(model, datamodule)
    
    # 训练完成后分析
    print("\n=== 训练完成，进行最终分析 ===")
    
    # 加载最佳模型
    best_model_path = trainer.checkpoint_callback.best_model_path
    if best_model_path:
        best_model = BalancedBarGpt4Trainer.load_from_checkpoint(best_model_path)
        best_model.eval()
        
        # 分析模型预测
        val_dataloader = datamodule.val_dataloader()
        analysis_results = analyze_model_predictions(
            best_model.model, 
            val_dataloader, 
            device='cuda' if args.gpus > 0 else 'cpu'
        )
        
        print(f"最终预测多样性: {analysis_results['pred_diversity']:.2%}")


if __name__ == "__main__":
    main()
