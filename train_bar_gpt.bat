e:
cd e:\lab\RoboQuant\pylab

@REM =================FUT==================
@REM d_model 维度选择：128，256 对于词汇量为40002的模型
@REM d_model=128就够了，可以减少参数量

python .\pyqlab\pl\train_bar_gpt.py ^
--model_name bar_gpt4 ^
--version GPT4 ^
--model_path .gpt ^
--data_path f:/featdata/barenc/db2 ^
--market fut ^
--block_name top ^
--period min1 ^
--time_encoding timeF ^
--pos_embed_type rope ^
--start_year 2025 ^
--end_year 2025 ^
--start_date "2025-02-01" ^
--end_date "2025-12-31" ^
--block_size 30 ^
--n_layer 4 ^
--n_head 16 ^
--d_model 96 ^
--batch_size 128 ^
--k_folds 3 ^
--max_epochs 3 ^
--lr 0.0005 ^
--min_delta 0.01 ^
--early_stop 5
@REM --resume
@REM --export_onnx ^
@REM --best_score 0.5

@REM python .\pyqlab\pl\train_bar_gpt.py ^
@REM --model_name bar_gpt4 ^
@REM --version GPT4 ^
@REM --model_path .gpt ^
@REM --data_path f:/featdata/barenc ^
@REM --market fut ^
@REM --block_name main ^
@REM --period day ^
@REM --time_encoding timeF ^
@REM --pos_embed_type rope ^
@REM --start_year 2010 ^
@REM --end_year 2025 ^
@REM --block_size 30 ^
@REM --n_layer 8 ^
@REM --n_head 16 ^
@REM --d_model 256 ^
@REM --batch_size 64 ^
@REM --k_folds 5 ^
@REM --max_epochs 4 ^
@REM --lr 0.0005 ^
@REM --min_delta 0.01 ^
@REM --early_stop 5

@REM python .\pyqlab\pl\train_bar_gpt.py ^
@REM --model_name bar_gpt4 ^
@REM --version GPT4 ^
@REM --model_path .gpt ^
@REM --data_path f:/featdata/barenc ^
@REM --market fut ^
@REM --block_name main ^
@REM --period day ^
@REM --time_encoding timeF ^
@REM --pos_embed_type rope ^
@REM --start_year 2010 ^
@REM --end_year 2025 ^
@REM --block_size 30 ^
@REM --n_layer 8 ^
@REM --n_head 16 ^
@REM --d_model 256 ^
@REM --batch_size 64 ^
@REM --k_folds 5 ^
@REM --max_epochs 3 ^
@REM --lr 0.0005 ^
@REM --min_delta 0.01 ^
@REM --early_stop 5
@REM  ^
@REM --export_onnx ^
@REM --best_score 0.5


@REM =================SF==================

@REM python .\pyqlab\pl\train_bar_gpt.py ^
@REM --model_name bar_gpt4 ^
@REM --version GPT4 ^
@REM --model_path .gpt ^
@REM --data_path f:/featdata/barenc ^
@REM --market fut ^
@REM --block_name sf ^
@REM --period min5 ^
@REM --time_encoding timeF ^
@REM --pos_embed_type rope ^
@REM --start_year 2019 ^
@REM --end_year 2025 ^
@REM --block_size 40 ^
@REM --n_layer 8 ^
@REM --n_head 16 ^
@REM --d_model 256 ^
@REM --batch_size 64 ^
@REM --k_folds 5 ^
@REM --max_epochs 3 ^
@REM --lr 0.0005 ^
@REM --min_delta 0.01 ^
@REM --early_stop 5
@REM --export_onnx ^
@REM --best_score 0.13

@REM python .\pyqlab\pl\train_bar_gpt.py ^
@REM --model_name bar_gpt4 ^
@REM --version GPT4 ^
@REM --model_path .gpt ^
@REM --data_path f:/featdata/barenc/db ^
@REM --market fut ^
@REM --block_name sf ^
@REM --period min5 ^
@REM --time_encoding timeF ^
@REM --pos_embed_type rope ^
@REM --start_year 2021 ^
@REM --end_year 2025 ^
@REM --block_size 40 ^
@REM --n_layer 8 ^
@REM --n_head 16 ^
@REM --d_model 256 ^
@REM --batch_size 64 ^
@REM --k_folds 5 ^
@REM --max_epochs 3 ^
@REM --lr 0.0005 ^
@REM --min_delta 0.01 ^
@REM --early_stop 5
@REM  ^
@REM --resume
@REM --export_onnx ^
@REM --best_score 0.13

@REM python .\pyqlab\pl\train_bar_gpt.py ^
@REM --model_name bar_gpt4 ^
@REM --version GPT4 ^
@REM --model_path .gpt ^
@REM --data_path f:/featdata/barenc ^
@REM --market fut ^
@REM --block_name sf ^
@REM --period day ^
@REM --time_encoding timeF ^
@REM --pos_embed_type rope ^
@REM --start_year 2000 ^
@REM --end_year 2025 ^
@REM --block_size 20 ^
@REM --n_layer 8 ^
@REM --n_head 16 ^
@REM --d_model 256 ^
@REM --batch_size 64 ^
@REM --k_folds 5 ^
@REM --max_epochs 3 ^
@REM --lr 0.0005 ^
@REM --min_delta 0.01 ^
@REM --early_stop 5
