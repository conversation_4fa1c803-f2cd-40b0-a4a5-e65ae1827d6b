"""
## GPT-2 to GPT-4的主要优化和变更包括：
- 使用RMSNorm替代LayerNorm，这是GPT-4中的一个改进。
- 实现了更高效的RotaryEmbedding，并在Attention模块中应用。
- 简化了Attention模块，使用PyTorch的scaled_dot_product_attention函数，
  这在支持的硬件上可以提供更好的性能。
- 调整了MLP结构，使用GELU激活函数。
- 在Block中使用了更现代的残差连接方式。
- 优化了权重初始化方法。
- 移除了MoE（Mixture of Experts）相关的代码，因为标准的GPT-4模型不使用MoE。
  如果你特别需要MoE，可以考虑将其作为一个可选的特性。
- 简化了forward方法，使其更加清晰和高效。
  这些变更应该能够提高模型的性能和训练稳定性。
  请注意，这些修改可能需要相应地调整训练脚本和其他相关代码。
  此外，你可能需要根据具体的任务和数据集来微调超参数。
"""

import math
import os
import torch
import torch.nn as nn
from torch.nn import functional as F
from pyqlab.models.layers.Embed import (
    TimeFeatureEmbedding, TemporalEmbedding,
    PeriodicTimeEncoding, RelativeTimeEncoding,
    ContinuousTimeEmbedding, MultiScaleTimeEncoding,
    AdaptiveTimeEncoding
)
from pyqlab.data.dataset.utils import get_vocab
class RMSNorm(nn.Module):
    """
    RMSNorm层
    使用均方根归一化进行层归一化。
    """
    def __init__(self, dim, eps=1e-6):
        super().__init__()
        self.eps = eps
        self.weight = nn.Parameter(torch.ones(dim))

    def _norm(self, x):
        return x * torch.rsqrt(x.pow(2).mean(-1, keepdim=True) + self.eps)

    def forward(self, x):
        return self._norm(x) * self.weight

class RotaryEmbedding(nn.Module):
    """
    RoPE（Rotary Position Embedding）
    使用旋转位置嵌入进行位置编码。
    """
    def __init__(self, dim, max_position_embeddings=2048, base=10000):
        super().__init__()
        inv_freq = 1.0 / (base ** (torch.arange(0, dim, 2).float() / dim))
        self.register_buffer("inv_freq", inv_freq)
        self.max_seq_len_cached = max_position_embeddings
        t = torch.arange(self.max_seq_len_cached, device=self.inv_freq.device).type_as(self.inv_freq)
        freqs = torch.einsum("i,j->ij", t, self.inv_freq)
        emb = torch.cat((freqs, freqs), dim=-1)
        self.register_buffer("cos_cached", emb.cos()[None, None, :, :], persistent=False)
        self.register_buffer("sin_cached", emb.sin()[None, None, :, :], persistent=False)

    def forward(self, x, seq_len=None):
        if seq_len > self.max_seq_len_cached:
            self.max_seq_len_cached = seq_len
            t = torch.arange(self.max_seq_len_cached, device=x.device).type_as(self.inv_freq)
            freqs = torch.einsum("i,j->ij", t, self.inv_freq)
            emb = torch.cat((freqs, freqs), dim=-1).to(x.device)
            self.register_buffer("cos_cached", emb.cos()[None, None, :, :], persistent=False)
            self.register_buffer("sin_cached", emb.sin()[None, None, :, :], persistent=False)
        return (
            self.cos_cached[:, :, :seq_len, ...].to(dtype=x.dtype),
            self.sin_cached[:, :, :seq_len, ...].to(dtype=x.dtype),
        )

def rotate_half(x):
    """
    将输入向量的后半部分旋转到前半部分。
    """
    x1, x2 = x[..., : x.shape[-1] // 2], x[..., x.shape[-1] // 2 :]
    return torch.cat((-x2, x1), dim=-1)

def apply_rotary_pos_emb(q, k, cos, sin, offset=0):
    """
    应用旋转位置嵌入到查询和键向量。
    """
    # 确保cos和sin的维度与q、k匹配
    # q, k的形状: (batch_size, n_heads, seq_len, head_dim)
    # cos, sin的形状: (1, 1, seq_len, head_dim)

    # 如果cos和sin的最后一个维度与head_dim不匹配，需要调整
    if cos.size(-1) != q.size(-1):
        # 截取或重复以匹配head_dim
        head_dim = q.size(-1)
        if cos.size(-1) > head_dim:
            cos = cos[..., :head_dim]
            sin = sin[..., :head_dim]
        else:
            # 如果cos/sin维度小于head_dim，重复填充
            repeat_factor = head_dim // cos.size(-1)
            remainder = head_dim % cos.size(-1)
            cos_repeated = cos.repeat(1, 1, 1, repeat_factor)
            sin_repeated = sin.repeat(1, 1, 1, repeat_factor)
            if remainder > 0:
                cos_repeated = torch.cat([cos_repeated, cos[..., :remainder]], dim=-1)
                sin_repeated = torch.cat([sin_repeated, sin[..., :remainder]], dim=-1)
            cos = cos_repeated
            sin = sin_repeated

    q_embed = (q * cos) + (rotate_half(q) * sin)
    k_embed = (k * cos) + (rotate_half(k) * sin)
    return q_embed, k_embed

class MLP(nn.Module):
    """
    MLP（Multi-Layer Perceptron）
    包含两个全连接层和GELU激活函数。
    """
    def __init__(self, dim, hidden_dim, bias=True):
        super().__init__()
        self.c_fc = nn.Linear(dim, hidden_dim, bias=bias)
        self.c_proj = nn.Linear(hidden_dim, dim, bias=bias)
        self.act = nn.GELU()

    def forward(self, x):
        return self.c_proj(self.act(self.c_fc(x)))

class AlibiPositionalEmbedding(nn.Module):
    """
    ALiBi（Attention with Linear Biases）位置嵌入
    使用线性偏置进行位置嵌入，适用于处理变长序列和外推。
    """
    def __init__(self, num_heads, max_seq_len=2048):
        super().__init__()
        self.num_heads = num_heads
        self.max_seq_len = max_seq_len
        slopes = torch.Tensor(self._get_slopes(num_heads))
        self.register_buffer("slopes", slopes)
        self.register_buffer("bias", self._get_bias(max_seq_len))

    def _get_slopes(self, num_heads):
        def get_slopes_power_of_2(n):
            start = (2**(-2**-(math.log2(n)-3)))
            ratio = start
            return [start*ratio**i for i in range(n)]

        if math.log2(num_heads).is_integer():
            return get_slopes_power_of_2(num_heads)
        else:
            closest_power_of_2 = 2**math.floor(math.log2(num_heads))
            return get_slopes_power_of_2(closest_power_of_2) + self._get_slopes(2*closest_power_of_2)[0::2][:num_heads-closest_power_of_2]

    def _get_bias(self, max_seq_len):
        bias = torch.arange(max_seq_len).unsqueeze(0).unsqueeze(0)
        return bias * self.slopes.unsqueeze(1).unsqueeze(1)

    def forward(self, x, seq_len):
        return self.bias[:, :, :seq_len, :seq_len]

class Attention(nn.Module):
    """
    注意力机制
    支持RoPE和ALiBi位置嵌入。
    """
    def __init__(self, dim, n_heads, dropout, pos_embed_type='rope', bias=True):
        super().__init__()
        self.n_heads = n_heads
        self.dim = dim
        self.head_dim = dim // n_heads
        self.wq = nn.Linear(dim, dim, bias=bias)
        self.wk = nn.Linear(dim, dim, bias=bias)
        self.wv = nn.Linear(dim, dim, bias=bias)
        self.attn_dropout = nn.Dropout(dropout)
        self.resid_dropout = nn.Dropout(dropout)
        self.proj = nn.Linear(dim, dim, bias=bias)
        self.pos_embed_type = pos_embed_type

        if pos_embed_type == 'alibi':
            self.alibi = AlibiPositionalEmbedding(n_heads)

    def forward(self, x, rotary_emb=None):
        B, T, C = x.size()
        q, k, v = self.wq(x), self.wk(x), self.wv(x)
        q = q.view(B, T, self.n_heads, C // self.n_heads).transpose(1, 2)
        k = k.view(B, T, self.n_heads, C // self.n_heads).transpose(1, 2)
        v = v.view(B, T, self.n_heads, C // self.n_heads).transpose(1, 2)

        if self.pos_embed_type == 'rope':
            cos, sin = rotary_emb
            q, k = apply_rotary_pos_emb(q, k, cos, sin)
            attn_bias = None
        elif self.pos_embed_type == 'alibi':
            attn_bias = self.alibi(x, T)
        else:
            attn_bias = None

        y = F.scaled_dot_product_attention(q, k, v, attn_mask=attn_bias, dropout_p=self.attn_dropout.p if self.training else 0, is_causal=True)
        y = y.transpose(1, 2).contiguous().view(B, T, C)
        y = self.resid_dropout(self.proj(y))
        return y

class Block(nn.Module):
    """
    Transformer Block
    包含RMSNorm、注意力机制和MLP。
    """
    def __init__(self, dim, n_heads, dropout, pos_embed_type='rope', bias=True):
        super().__init__()
        self.ln_1 = RMSNorm(dim)
        self.attn = Attention(dim, n_heads, dropout, pos_embed_type, bias)
        self.ln_2 = RMSNorm(dim)
        self.mlp = MLP(dim, 4 * dim, bias)
        self.pos_embed_type = pos_embed_type

    def forward(self, x, rotary_emb=None):
        x = x + self.attn(self.ln_1(x), rotary_emb)
        x = x + self.mlp(self.ln_2(x))
        return x

class BarGpt4(nn.Module):
    """
    BarGpt4模型
    支持多种时间编码嵌入方法和特征向量合并方法。

    模型特点：
    1. 使用RMSNorm替代传统LayerNorm
    2. 实现旋转位置编码（RoPE）及ALiBi
    3. 支持多种时间特征编码方法
    4. 使用现代化残差连接
    5. 优化的权重初始化
    6. 支持训练和推理两种工作模式
    7. 支持导出ONNX格式推理模型
    """
    def __init__(self, block_size, code_size, vocab_size, n_layer, n_head, d_model, time_encoding,
                 time_embed_type='periodic', freq='t', pos_embed_type='rope', dropout=0.1, bias=True, **kwargs):
        """
        初始化BarGpt4模型。

        参数:
        - block_size: 序列块大小
        - code_size: 代码嵌入大小
        - vocab_size: 词汇表大小
        - n_layer: Transformer层数
        - n_head: 注意力头数
        - d_model: 嵌入维度
        - time_encoding: 时间编码类型
        - time_embed_type: 时间编码嵌入方法
        - freq: 时间频率
        - pos_embed_type: 位置嵌入方法
        - dropout: Dropout概率
        - bias: 是否使用偏置项
        """
        super().__init__()
        self.block_size = block_size
        self.vocab_size = vocab_size
        self.n_layer = n_layer
        self.n_head = n_head
        self.d_model = d_model
        self.time_encoding = time_encoding
        self.pos_embed_type = pos_embed_type
        self.time_embed_type = time_embed_type
        self.freq = freq
        self.bias = bias
        self.is_training = True  # 默认为训练模式

        # 确保d_model能被n_head整除
        assert d_model % n_head == 0, f"d_model ({d_model}) must be divisible by n_head ({n_head})"
        self.head_dim = d_model // n_head

        # 存储模型配置，用于估算MFU
        self.config = {
            'n_layer': n_layer,
            'n_head': n_head,
            'd_model': d_model,
            'block_size': block_size
        }

        self.transformer = nn.ModuleDict(dict(
            bar_eb = nn.Embedding(vocab_size, d_model),
            code_eb = nn.Embedding(code_size, d_model),
            tf_eb = TimeFeatureEmbedding(d_model=d_model, freq=freq) if time_encoding =='timeF' else TemporalEmbedding(d_model=d_model, embed_type=time_encoding, freq=freq),
            time_eb = self._get_time_embedding(time_embed_type, d_model),
            drop = nn.Dropout(dropout),
            h = nn.ModuleList([Block(d_model, n_head, dropout, pos_embed_type, bias) for _ in range(n_layer)]),
            ln_f = RMSNorm(d_model),
        ))
        self.lm_head = nn.Linear(d_model, vocab_size, bias=False)

        if pos_embed_type == 'rope':
            self.rotary_emb = RotaryEmbedding(self.head_dim)
        elif pos_embed_type == 'alibi':
            self.alibi = AlibiPositionalEmbedding(n_head, block_size)

        # Initialize weights
        self.apply(self._init_weights)
        for pn, p in self.named_parameters():
            if pn.endswith('proj.weight'):
                torch.nn.init.normal_(p, mean=0.0, std=0.02/math.sqrt(2 * n_layer))

        # Weight tying
        self.transformer.bar_eb.weight = self.lm_head.weight

        print("="*30)
        print(f"Number of parameters: {sum(p.numel() for p in self.parameters())/1e6:.2f}M")
        print("="*30)

    def _init_weights(self, module):
        """
        初始化权重
        """
        if isinstance(module, (nn.Linear, nn.Embedding)):
            module.weight.data.normal_(mean=0.0, std=0.02)
        elif isinstance(module, nn.LayerNorm):
            module.bias.data.zero_()
            module.weight.data.fill_(1.0)
        if isinstance(module, nn.Linear) and module.bias is not None:
            module.bias.data.zero_()

    def _get_time_embedding(self, time_embed_type, d_model):
        """
        获取时间编码嵌入方法。

        参数:
        - time_embed_type: 时间编码嵌入方法
        - d_model: 嵌入维度

        返回:
        - 时间编码嵌入层
        """
        if time_embed_type == 'periodic':
            return PeriodicTimeEncoding(d_model=d_model, max_len=self.block_size)
        elif time_embed_type == 'relative':
            return RelativeTimeEncoding(d_model=d_model, max_len=self.block_size)
        elif time_embed_type == 'time_feature':
            return TimeFeatureEmbedding(d_model=d_model, freq=self.freq)
        elif time_embed_type == 'continuous':
            return ContinuousTimeEmbedding(d_model=d_model)
        elif time_embed_type == 'multiscale':
            return MultiScaleTimeEncoding(d_model=d_model)
        elif time_embed_type == 'adaptive':
            return AdaptiveTimeEncoding(d_model=d_model, max_len=self.block_size)
        else:
            raise ValueError(f"Unknown time embedding type: {time_embed_type}")

    def forward(self, code, x, x_mark, targets=None, class_weights=None):
        """
        前向传播。

        参数:
        - code: 代码输入，形状为(batch_size, seq_len)
        - x: 序列输入，形状为(batch_size, seq_len)
        - x_mark: 时间标记输入，形状为(batch_size, seq_len, mark_dim)
        - targets: 目标输出（可选），形状为(batch_size, seq_len)
        - class_weights: 类别权重（可选），用于处理类别不平衡

        返回:
        - logits: 模型输出，若targets为None则仅返回最后一个时间步的logits
        - loss: 损失值（如果提供了目标输出）；否则为None
        """
        b, t = x.size()
        assert t <= self.block_size, f"Cannot forward sequence of length {t}, block size is only {self.block_size}"

        bar_emb = self.transformer.bar_eb(x)
        code_emb = self.transformer.code_eb(code)
        if self.time_embed_type == 'time_feature':
            time_emb = self.transformer.tf_eb(x_mark if self.freq == 't' else x_mark[:, :, -3:])
        else:
            time_emb = self.transformer.tf_eb(x_mark if self.freq == 't' else x_mark[:, :, -3:])
            time_emb = self.transformer.time_eb(time_emb)

        x = self.transformer.drop(bar_emb + 0.1 * code_emb + 0.2 * time_emb)

        if self.pos_embed_type == 'rope':
            rotary_emb = self.rotary_emb(x, seq_len=t)
        else:
            rotary_emb = None

        for block in self.transformer.h:
            x = block(x, rotary_emb)
        x = self.transformer.ln_f(x)

        if self.is_training and targets is not None:
            logits = self.lm_head(x)
            loss = self._compute_balanced_loss(logits, targets, class_weights)
            return logits, loss
        else:
            # 推理模式下只返回最后一个时间步的logits
            logits = self.lm_head(x[:, [-1], :])
            return logits, None

    def _compute_balanced_loss(self, logits, targets, class_weights=None):
        """
        计算平衡损失函数，解决token分布不均衡问题

        参数:
        - logits: 模型输出logits，形状为(batch_size, seq_len, vocab_size)
        - targets: 目标token，形状为(batch_size, seq_len)
        - class_weights: 类别权重，形状为(vocab_size,)

        返回:
        - loss: 平衡后的损失值
        """
        # 重塑为2D
        logits_flat = logits.view(-1, logits.size(-1))
        targets_flat = targets.view(-1)

        # 创建有效mask（忽略-1）
        valid_mask = targets_flat != -1

        if valid_mask.sum() == 0:
            return torch.tensor(0.0, device=logits.device, requires_grad=True)

        valid_logits = logits_flat[valid_mask]
        valid_targets = targets_flat[valid_mask]

        # 方法1: 使用类别权重的交叉熵损失
        if class_weights is not None:
            loss = F.cross_entropy(
                valid_logits,
                valid_targets,
                weight=class_weights,
                label_smoothing=0.05  # 轻微的标签平滑
            )
        else:
            # 方法2: Focal Loss - 自动处理类别不平衡
            loss = self._focal_loss(valid_logits, valid_targets, alpha=0.25, gamma=2.0)

        # 方法3: 添加多样性正则化
        diversity_loss = self._diversity_regularization(valid_logits)

        # 组合损失
        total_loss = loss + 0.1 * diversity_loss

        return total_loss

    def _focal_loss(self, logits, targets, alpha=0.25, gamma=2.0):
        """
        Focal Loss实现，自动处理类别不平衡

        参数:
        - logits: 预测logits
        - targets: 真实标签
        - alpha: 平衡因子
        - gamma: 聚焦参数
        """
        ce_loss = F.cross_entropy(logits, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = alpha * (1 - pt) ** gamma * ce_loss
        return focal_loss.mean()

    def _diversity_regularization(self, logits):
        """
        多样性正则化，鼓励模型产生多样化的预测

        参数:
        - logits: 预测logits
        """
        # 计算预测分布的熵
        probs = F.softmax(logits, dim=-1)
        entropy = -torch.sum(probs * torch.log(probs + 1e-8), dim=-1)

        # 鼓励高熵（多样性）
        target_entropy = torch.log(torch.tensor(self.vocab_size / 4.0))  # 目标熵
        diversity_loss = F.relu(target_entropy - entropy.mean())

        return diversity_loss

    def get_num_params(self):
        """
        获取模型参数数量。

        返回:
        - 模型参数数量
        """
        return sum(p.numel() for p in self.parameters())

    def crop_block_size(self, block_size):
        """
        裁剪序列块大小。

        参数:
        - block_size: 新的序列块大小
        """
        # 修改模型以减小块大小（如有必要）
        assert block_size <= self.block_size
        self.block_size = block_size

        # 更新相关的位置编码组件
        if self.pos_embed_type == 'alibi':
            self.alibi = AlibiPositionalEmbedding(self.n_head, block_size)

        # 更新时间编码（如果使用了依赖于max_len的编码）
        if self.time_embed_type in ['periodic', 'relative', 'adaptive']:
            self.transformer.time_eb = self._get_time_embedding(self.time_embed_type, self.d_model)

        # 更新配置
        self.config['block_size'] = block_size

    def configure_optimizers(self, weight_decay, learning_rate, betas, device_type):
        """
        配置优化器。

        参数:
        - weight_decay: 权重衰减系数
        - learning_rate: 学习率
        - betas: Adam优化器的beta参数
        - device_type: 设备类型

        返回:
        - 优化器
        """
        import inspect
        # start with all of the candidate parameters
        param_dict = {pn: p for pn, p in self.named_parameters()}
        # filter out those that do not require grad
        param_dict = {pn: p for pn, p in param_dict.items() if p.requires_grad}
        # create optim groups. Any parameters that is 2D will be weight decayed, otherwise no.
        # i.e. all weight tensors in matmuls + embeddings decay, all biases and layernorms don't.
        decay_params = [p for n, p in param_dict.items() if p.dim() >= 2]
        nodecay_params = [p for n, p in param_dict.items() if p.dim() < 2]
        optim_groups = [
            {'params': decay_params, 'weight_decay': weight_decay},
            {'params': nodecay_params, 'weight_decay': 0.0}
        ]
        num_decay_params = sum(p.numel() for p in decay_params)
        num_nodecay_params = sum(p.numel() for p in nodecay_params)
        print(f"num decayed parameter tensors: {len(decay_params)}, with {num_decay_params:,} parameters")
        print(f"num non-decayed parameter tensors: {len(nodecay_params)}, with {num_nodecay_params:,} parameters")
        # Create AdamW optimizer and use the fused version if it is available
        fused_available = 'fused' in inspect.signature(torch.optim.AdamW).parameters
        use_fused = fused_available and device_type == 'cuda'
        extra_args = dict(fused=True) if use_fused else dict()
        optimizer = torch.optim.AdamW(optim_groups, lr=learning_rate, betas=betas, **extra_args)
        print(f"using fused AdamW: {use_fused}")

        return optimizer

    def estimate_mfu(self, fwdbwd_per_iter, dt):
        """
        估计模型FLOPs利用率（MFU），单位为A100 bfloat16峰值FLOPS。

        参数:
        - fwdbwd_per_iter: 每次迭代的前向传播和反向传播次数
        - dt: 迭代时间

        返回:
        - MFU: 模型FLOPs利用率
        """
        # 首先估计每次迭代的FLOPs数量
        # 参考PaLM论文附录B: https://arxiv.org/abs/2204.02311
        N = self.get_num_params()
        L, H, Q, T = self.config['n_layer'], self.config['n_head'], self.config['d_model']//self.config['n_head'], self.config['block_size']
        flops_per_token = 6*N + 12*L*H*Q*T
        flops_per_fwdbwd = flops_per_token * T
        flops_per_iter = flops_per_fwdbwd * fwdbwd_per_iter
        # 将FLOPS吞吐量表示为A100 bfloat16峰值FLOPS的比率
        flops_achieved = flops_per_iter * (1.0/dt) # 每秒
        flops_promised = 312e12 # A100 GPU bfloat16峰值FLOPS为312 TFLOPS
        mfu = flops_achieved / flops_promised
        return mfu

    def train_mode(self):
        """
        设置模型为训练模式。
        """
        self.is_training = True
        self.train()
        return self

    def inference_mode(self):
        """
        设置模型为推理模式。
        """
        self.is_training = False
        self.eval()
        return self

    @staticmethod
    def generate_next_token(logits, temperature=0.1, top_k=1):
        # 获取最后一步的logits并应用温度
        logits = logits[:, -1, :] / temperature

        # 可选地只保留top-k选项
        if top_k is not None:
            v, _ = torch.topk(logits, min(top_k, logits.size(-1)))
            logits[logits < v[:, [-1]]] = -float('Inf')

        # 应用softmax转换logits为概率
        probs = F.softmax(logits, dim=-1)

        # 从分布中采样
        next_token = torch.multinomial(probs, num_samples=1)
        return next_token

    @torch.no_grad()
    def generate(self, code, x, x_mark, max_new_tokens, temperature=1.0, top_k=None):
        """
        根据输入序列生成新的标记。

        参数:
        - code: 代码输入，形状为(batch_size, seq_len)
        - x: 序列输入，形状为(batch_size, seq_len)
        - x_mark: 时间标记输入，形状为(batch_size, seq_len, mark_dim)
        - max_new_tokens: 最大生成的新标记数
        - temperature: 生成时的温度参数，控制采样的随机性
        - top_k: 仅从前k个可能性最大的标记中采样

        返回:
        - 生成的序列，形状为(batch_size, seq_len+max_new_tokens)
        """
        # 确保模型处于推理模式
        prev_mode = self.is_training
        self.inference_mode()

        # 保存原始输入以便输出
        original_x = x.clone()

        for _ in range(max_new_tokens):
            # 如果序列长度超过block_size，则截断
            if x.size(1) > self.block_size:
                x = x[:, -self.block_size:]
                code = code[:, -self.block_size:]
                x_mark = x_mark[:, -self.block_size:]

            # 前向传播获取下一个标记的预测
            logits, _ = self(code, x, x_mark)

            # 获取最后一步的logits并应用温度
            logits = logits[:, -1, :] / temperature

            # 可选地只保留top-k选项
            if top_k is not None:
                v, _ = torch.topk(logits, min(top_k, logits.size(-1)))
                logits[logits < v[:, [-1]]] = -float('Inf')

            # 应用softmax转换logits为概率
            probs = F.softmax(logits, dim=-1)

            # 从分布中采样
            next_token = torch.multinomial(probs, num_samples=1)

            # 将采样的标记附加到运行序列
            x = torch.cat((x, next_token), dim=1)

            # 为code和x_mark扩展最后一个标记（假设它们在生成过程中保持静态或可预测）
            code = torch.cat((code, code[:, [-1]]), dim=1)
            x_mark = torch.cat((x_mark, x_mark[:, [-1]]), dim=1)

        # 恢复原来的模式
        if prev_mode:
            self.train_mode()

        # 返回完整的生成序列，包括原始输入
        return torch.cat((original_x, x[:, original_x.size(1):]), dim=1)

    def export_onnx(self, save_path, batch_size=1, seq_len=None, dynamic_axes=True, opset_version=13):
        """
        将模型导出为ONNX格式。

        参数:
        - save_path: 保存路径，应以.onnx结尾
        - batch_size: 批处理大小
        - seq_len: 序列长度，如果为None则使用block_size
        - dynamic_axes: 是否使用动态轴（用于支持可变长度输入）
        - opset_version: ONNX操作集版本

        返回:
        - 导出是否成功
        """
        try:
            import onnx
            import onnxruntime
        except ImportError:
            print("请安装onnx和onnxruntime: pip install onnx onnxruntime")
            return False

        # 确保模型处于推理模式
        self.inference_mode()

        # 设置序列长度
        if seq_len is None:
            seq_len = min(64, self.block_size)  # 使用较小的默认值以加快导出速度

        # 创建示例输入
        dummy_code = torch.zeros((batch_size, seq_len), dtype=torch.long)
        dummy_x = torch.zeros((batch_size, seq_len), dtype=torch.long)
        dummy_x_mark = torch.zeros((batch_size, seq_len, 3 if self.freq != 't' else 1), dtype=torch.float)

        # 定义动态轴（如果需要）
        dynamic_axes_dict = None
        if dynamic_axes:
            dynamic_axes_dict = {
                'code': {0: 'batch_size', 1: 'seq_len'},
                'x': {0: 'batch_size', 1: 'seq_len'},
                'x_mark': {0: 'batch_size', 1: 'seq_len'},
                'output': {0: 'batch_size', 1: 'seq_len'}
            }

        # 创建一个前向传播函数，只返回logits
        def forward_for_export(code, x, x_mark):
            logits, _ = self(code, x, x_mark)
            return logits

        # 导出模型
        try:
            torch.onnx.export(
                self,  # 模型实例
                (dummy_code, dummy_x, dummy_x_mark),  # 模型输入
                save_path,  # 保存路径
                export_params=True,  # 存储训练后的参数权重
                opset_version=opset_version,  # ONNX版本
                do_constant_folding=True,  # 是否执行常量折叠优化
                input_names=['code', 'x', 'x_mark'],  # 输入名称
                output_names=['output'],  # 输出名称
                dynamic_axes=dynamic_axes_dict,  # 动态轴
                verbose=False
            )

            # 验证导出的模型
            onnx_model = onnx.load(save_path)
            onnx.checker.check_model(onnx_model)

            print(f"模型已成功导出到 {save_path}")
            print(f"输入形状: code={dummy_code.shape}, x={dummy_x.shape}, x_mark={dummy_x_mark.shape}")

            # 输出模型信息
            print(f"模型信息:")
            print(f"- 操作集版本: {opset_version}")
            print(f"- 动态轴: {'是' if dynamic_axes else '否'}")
            print(f"- 模型大小: {os.path.getsize(save_path) / (1024 * 1024):.2f} MB")

            return True

        except Exception as e:
            print(f"导出ONNX模型时出错: {str(e)}")
            return False

    def inference_with_onnx(self, onnx_path, code, x, x_mark):
        """
        使用ONNX运行时进行推理。

        参数:
        - onnx_path: ONNX模型路径
        - code: 代码输入
        - x: 序列输入
        - x_mark: 时间标记输入

        返回:
        - 模型输出
        """
        try:
            import onnxruntime as ort
        except ImportError:
            print("请安装onnxruntime: pip install onnxruntime")
            return None

        # 创建ONNX运行时会话
        session = ort.InferenceSession(onnx_path)

        # 准备输入
        ort_inputs = {
            'code': code.cpu().numpy(),
            'x': x.cpu().numpy(),
            'x_mark': x_mark.cpu().numpy()
        }

        # 运行推理
        ort_outputs = session.run(None, ort_inputs)

        # 将输出转换回PyTorch张量
        return torch.tensor(ort_outputs[0])

    def save_model(self, save_path, optimizer=None, epoch=None, loss=None):
        """
        保存模型和训练状态。

        参数:
        - save_path: 保存路径
        - optimizer: 优化器（可选）
        - epoch: 当前训练轮次（可选）
        - loss: 当前损失值（可选）

        返回:
        - 保存是否成功
        """
        try:
            checkpoint = {
                'model_state_dict': self.state_dict(),
                'config': {
                    'block_size': self.block_size,
                    'vocab_size': self.vocab_size,
                    'n_layer': self.n_layer,
                    'n_head': self.n_head,
                    'd_model': self.d_model,
                    'time_encoding': self.time_encoding,
                    'time_embed_type': self.time_embed_type,
                    'freq': self.freq,
                    'pos_embed_type': self.pos_embed_type
                }
            }

            if optimizer is not None:
                checkpoint['optimizer_state_dict'] = optimizer.state_dict()
            if epoch is not None:
                checkpoint['epoch'] = epoch
            if loss is not None:
                checkpoint['loss'] = loss

            torch.save(checkpoint, save_path)
            print(f"模型已保存到 {save_path}")
            return True
        except Exception as e:
            print(f"保存模型时出错: {str(e)}")
            return False

    @classmethod
    def load_model(cls, load_path, device='cpu'):
        """
        加载模型和训练状态。

        参数:
        - load_path: 加载路径
        - device: 设备类型

        返回:
        - 加载的模型和训练状态
        """
        try:
            checkpoint = torch.load(load_path, map_location=device)
            config = checkpoint['config']

            # 创建模型实例
            model = cls(
                block_size=config['block_size'],
                code_size=config.get('code_size', 100),  # 默认值，如果没有保存
                vocab_size=config['vocab_size'],
                n_layer=config['n_layer'],
                n_head=config['n_head'],
                d_model=config['d_model'],
                time_encoding=config['time_encoding'],
                time_embed_type=config['time_embed_type'],
                freq=config['freq'],
                pos_embed_type=config['pos_embed_type']
            )

            # 加载模型参数
            model.load_state_dict(checkpoint['model_state_dict'])
            model.to(device)

            # 返回模型和其他训练状态
            return {
                'model': model,
                'optimizer_state_dict': checkpoint.get('optimizer_state_dict'),
                'epoch': checkpoint.get('epoch'),
                'loss': checkpoint.get('loss')
            }
        except Exception as e:
            print(f"加载模型时出错: {str(e)}")
            return None

