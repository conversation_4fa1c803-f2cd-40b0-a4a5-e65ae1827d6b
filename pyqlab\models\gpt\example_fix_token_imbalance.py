"""
解决Token分布不平衡问题的完整示例
演示如何使用新的平衡训练工具
"""

import os
import sys
import torch
import numpy as np
from pathlib import Path
from collections import Counter

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from pyqlab.models.gpt.token_balance_utils import TokenBalancer, analyze_model_predictions
from pyqlab.models.gpt.bar_gpt4 import BarGpt4


def create_sample_imbalanced_data():
    """
    创建示例的不平衡token数据
    模拟token 795占比过高的情况
    """
    print("创建示例不平衡数据...")

    vocab_size = 1000
    num_sequences = 1000
    seq_length = 128

    # 创建严重不平衡的数据
    # Token 795 占50%，其他token分布不均
    token_sequences = []

    for _ in range(num_sequences):
        sequence = []
        for _ in range(seq_length):
            rand = np.random.random()
            if rand < 0.5:  # 50%概率是token 795
                token = 795
            elif rand < 0.7:  # 20%概率是其他几个高频token
                token = np.random.choice([100, 200, 300, 400])
            elif rand < 0.9:  # 20%概率是中频token
                token = np.random.choice(range(500, 600))
            else:  # 10%概率是低频token
                token = np.random.choice(range(0, vocab_size))

            sequence.append(token)

        token_sequences.append(sequence)

    print(f"创建了 {len(token_sequences)} 个序列，每个长度 {seq_length}")
    return token_sequences, vocab_size


def demonstrate_analysis():
    """演示token分布分析"""
    print("\n" + "="*60)
    print("步骤1: Token分布分析")
    print("="*60)

    # 创建示例数据
    token_sequences, vocab_size = create_sample_imbalanced_data()

    # 创建分析器
    balancer = TokenBalancer(vocab_size)

    # 分析分布
    stats = balancer.analyze_token_distribution(
        token_sequences,
        save_plot=True,
        plot_path="example_token_distribution.png"
    )

    # 检查是否发现了token 795的问题
    high_freq_found = False
    for token_id, count, percentage in stats['high_freq_tokens']:
        if token_id == 795:
            high_freq_found = True
            print(f"✅ 成功检测到问题: Token 795 占比 {percentage:.1%}")
            break

    if not high_freq_found:
        print("❌ 未检测到预期的高频token问题")

    return balancer, stats


def demonstrate_class_weights():
    """演示类别权重计算"""
    print("\n" + "="*60)
    print("步骤2: 计算类别权重")
    print("="*60)

    token_sequences, vocab_size = create_sample_imbalanced_data()
    balancer = TokenBalancer(vocab_size)
    balancer.analyze_token_distribution(token_sequences, save_plot=False)

    # 尝试不同的权重计算方法
    methods = ['inverse_freq', 'sqrt_inverse_freq', 'log_inverse_freq']

    for method in methods:
        print(f"\n--- {method} 方法 ---")
        weights = balancer.compute_class_weights(method=method, smooth_factor=1.0)

        # 检查token 795的权重
        weight_795 = weights[795].item()
        print(f"Token 795 的权重: {weight_795:.4f}")

        # 权重应该较低（因为频率高）
        if weight_795 < 1.0:
            print(f"✅ 权重计算正确: 高频token获得较低权重")
        else:
            print(f"❌ 权重计算可能有问题")


def demonstrate_resampling():
    """演示数据重采样"""
    print("\n" + "="*60)
    print("步骤3: 数据重采样")
    print("="*60)

    token_sequences, vocab_size = create_sample_imbalanced_data()
    balancer = TokenBalancer(vocab_size)
    balancer.analyze_token_distribution(token_sequences, save_plot=False)

    # 重采样前的分布
    all_tokens_before = []
    for seq in token_sequences:
        all_tokens_before.extend(seq)

    counter_before = Counter(all_tokens_before)
    freq_795_before = counter_before[795] / len(all_tokens_before)
    print(f"重采样前 Token 795 频率: {freq_795_before:.2%}")

    # 进行重采样
    resampled_sequences = balancer.resample_sequences(
        token_sequences,
        target_distribution='uniform',
        max_samples_per_class=50
    )

    # 重采样后的分布
    all_tokens_after = []
    for seq in resampled_sequences:
        all_tokens_after.extend(seq)

    counter_after = Counter(all_tokens_after)
    freq_795_after = counter_after[795] / len(all_tokens_after)
    print(f"重采样后 Token 795 频率: {freq_795_after:.2%}")

    if freq_795_after < freq_795_before:
        print(f"✅ 重采样成功: Token 795 频率从 {freq_795_before:.1%} 降至 {freq_795_after:.1%}")
    else:
        print(f"❌ 重采样效果不明显")


def demonstrate_balanced_loss():
    """演示平衡损失函数"""
    print("\n" + "="*60)
    print("步骤4: 平衡损失函数测试")
    print("="*60)

    # 创建模型实例
    model_config = {
        'block_size': 128,
        'code_size': 100,
        'vocab_size': 1000,
        'n_layer': 2,  # 小模型用于测试
        'n_head': 4,
        'd_model': 256,
        'time_encoding': 'timeF',
        'time_embed_type': 'periodic',
        'freq': 't',
        'pos_embed_type': 'rope',
        'dropout': 0.1
    }

    model = BarGpt4(**model_config)
    model.eval()

    # 创建测试数据
    batch_size = 8
    seq_len = 64

    code = torch.randint(0, 100, (batch_size, seq_len))
    x = torch.randint(0, 1000, (batch_size, seq_len))
    # 修正时间特征维度 - 对于freq='t'，TimeFeatureEmbedding期望5个特征
    x_mark = torch.randn(batch_size, seq_len, 5)

    # 创建不平衡的targets（大部分是795）
    targets = torch.full((batch_size, seq_len), 795)
    # 少量其他token
    targets[:, :10] = torch.randint(0, 1000, (batch_size, 10))

    # 计算类别权重
    token_sequences, vocab_size = create_sample_imbalanced_data()
    balancer = TokenBalancer(vocab_size)
    balancer.analyze_token_distribution(token_sequences, save_plot=False)
    class_weights = balancer.compute_class_weights(method='sqrt_inverse_freq')

    # 测试不同损失函数
    with torch.no_grad():
        # 标准交叉熵
        logits, loss_standard = model(code, x, x_mark, targets)
        print(f"标准交叉熵损失: {loss_standard:.4f}")

        # 平衡损失（带类别权重）
        logits, loss_balanced = model(code, x, x_mark, targets, class_weights=class_weights)
        print(f"平衡损失: {loss_balanced:.4f}")

        # 检查预测多样性
        predictions = logits.argmax(dim=-1)
        unique_preds = len(torch.unique(predictions))
        print(f"预测的唯一token数: {unique_preds}")

        # 统计预测分布
        pred_counter = Counter(predictions.flatten().tolist())
        most_common_pred = pred_counter.most_common(1)[0]
        print(f"最常见预测: Token {most_common_pred[0]} ({most_common_pred[1]}次)")


def demonstrate_complete_workflow():
    """演示完整的解决流程"""
    print("\n" + "="*60)
    print("完整解决流程演示")
    print("="*60)

    # 1. 问题诊断
    print("\n1. 问题诊断阶段")
    token_sequences, vocab_size = create_sample_imbalanced_data()
    balancer = TokenBalancer(vocab_size)
    stats = balancer.analyze_token_distribution(token_sequences, save_plot=False)

    # 检查是否存在严重不平衡
    gini_threshold = 0.7
    high_freq_threshold = 0.3

    # 计算基尼系数
    all_tokens = []
    for seq in token_sequences:
        all_tokens.extend(seq)
    token_counter = Counter(all_tokens)
    frequencies = [count/len(all_tokens) for count in token_counter.values()]
    frequencies.sort()
    n = len(frequencies)
    gini = (2 * sum((i+1) * freq for i, freq in enumerate(frequencies))) / (n * sum(frequencies)) - (n+1) / n

    print(f"基尼系数: {gini:.4f}")

    # 检查最高频token
    most_common = token_counter.most_common(1)[0]
    max_freq = most_common[1] / len(all_tokens)
    print(f"最高频token: {most_common[0]} ({max_freq:.1%})")

    # 2. 解决方案选择
    print("\n2. 解决方案选择")
    solutions = []

    if gini > gini_threshold:
        solutions.append("使用Focal Loss")
        solutions.append("计算类别权重")

    if max_freq > high_freq_threshold:
        solutions.append("数据重采样")
        solutions.append("多样性正则化")

    print("推荐解决方案:")
    for i, solution in enumerate(solutions, 1):
        print(f"  {i}. {solution}")

    # 3. 应用解决方案
    print("\n3. 应用解决方案")

    # 计算类别权重
    class_weights = balancer.compute_class_weights(method='sqrt_inverse_freq')
    print(f"✅ 已计算类别权重")

    # 数据重采样
    if max_freq > high_freq_threshold:
        resampled_sequences = balancer.resample_sequences(
            token_sequences,
            target_distribution='uniform'
        )
        print(f"✅ 已完成数据重采样")

    # 4. 效果验证
    print("\n4. 效果验证")
    print("建议在实际训练中监控以下指标:")
    print("  - 验证集预测多样性")
    print("  - 各token的预测频率")
    print("  - 模型收敛情况")

    print("\n✅ 完整流程演示完成！")


def main():
    """主函数"""
    print("Token分布不平衡问题解决方案演示")
    print("="*60)

    try:
        # 1. 分析演示
        demonstrate_analysis()

        # 2. 类别权重演示
        demonstrate_class_weights()

        # 3. 重采样演示
        demonstrate_resampling()

        # 4. 平衡损失演示
        demonstrate_balanced_loss()

        # 5. 完整流程演示
        demonstrate_complete_workflow()

        print("\n" + "="*60)
        print("所有演示完成！")
        print("="*60)

        print("\n下一步操作建议:")
        print("1. 使用 analyze_token_imbalance.py 分析您的实际数据")
        print("2. 根据分析结果选择合适的解决方案")
        print("3. 使用 train_balanced_bar_gpt4.py 进行平衡训练")
        print("4. 监控训练过程中的预测多样性")

    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
