# BarGpt4 回测系统杠杆交易功能指南

## 概述

BarGpt4回测系统现在支持杠杆交易功能，允许用户模拟期货等杠杆产品的交易。杠杆可以放大收益和风险，是期货交易的重要特征。

## 杠杆交易原理

### 什么是杠杆？

杠杆是指使用借入资金来增加投资头寸的金融工具。在期货交易中，投资者只需要支付合约价值的一小部分作为保证金，就可以控制整个合约的价值。

**公式：**
- 保证金 = 合约价值 / 杠杆倍数
- 盈亏 = (价格变动) × 杠杆倍数 × 持仓数量

### 杠杆的影响

1. **收益放大**：价格上涨时，杠杆会放大收益
2. **风险放大**：价格下跌时，杠杆也会放大亏损
3. **资金效率**：用更少的资金控制更大的头寸

## 使用方法

### 命令行参数

```bash
python backtest_bar_gpt4.py --leverage 2.0
```

### 参数说明

- `--leverage`: 杠杆倍数，默认为1.0（无杠杆）
  - 1.0: 无杠杆（现货交易）
  - 2.0: 2倍杠杆
  - 5.0: 5倍杠杆
  - 10.0: 10倍杠杆

### 示例

```bash
# 无杠杆交易
python backtest_bar_gpt4.py --data_path data.parquet --model_path model.pt --leverage 1.0

# 2倍杠杆交易
python backtest_bar_gpt4.py --data_path data.parquet --model_path model.pt --leverage 2.0

# 5倍杠杆交易
python backtest_bar_gpt4.py --data_path data.parquet --model_path model.pt --leverage 5.0
```

## 实现细节

### 交易逻辑修改

1. **持仓计算**：
   - 买入：`position = leverage`
   - 卖出：`position = -leverage`

2. **保证金计算**：
   - 保证金 = 合约价值 / 杠杆倍数
   - 只需要支付保证金，而不是全额资金

3. **盈亏计算**：
   - 盈亏会按杠杆倍数放大
   - 风险管理更加重要

### 代码示例

```python
# 创建带杠杆的回测器
backtester = BarGpt4Backtester(
    model=model,
    initial_capital=10000.0,
    leverage=2.0  # 2倍杠杆
)

# 进行回测
results = backtester.backtest(df=data, seq_len=30)
```

## 风险管理

### 杠杆风险

1. **放大亏损**：杠杆会同时放大收益和亏损
2. **保证金不足**：如果亏损过大，可能导致保证金不足
3. **强制平仓**：在实际交易中，保证金不足会导致强制平仓

### 建议

1. **合理杠杆**：建议使用2-5倍杠杆，避免过高杠杆
2. **风险控制**：设置合理的止损和止盈
3. **资金管理**：不要使用全部资金作为保证金

## 回测结果解读

### 新增指标

回测结果中会显示杠杆倍数：

```
回测结果摘要:
初始资金: 10000.00
杠杆倍数: 2.0x
最终权益: 12000.00
总收益率: 20.00%
年化收益率: 25.00%
...
```

### 交易记录

每笔交易记录中会包含杠杆信息：

```python
{
    'datetime': '2023-01-01 10:00:00',
    'action': 'BUY',
    'price': 5000.0,
    'position': 2.0,  # 杠杆倍数
    'leverage': 2.0,
    'margin_required': 2500.0,  # 所需保证金
    'capital': 7500.0,
    'equity': 10000.0,
    'profit': 0
}
```

## 注意事项

1. **模拟交易**：这是回测模拟，实际交易中的杠杆机制可能更复杂
2. **保证金制度**：实际期货交易有维持保证金要求
3. **强制平仓**：实际交易中会有强制平仓机制
4. **手续费**：杠杆交易的手续费可能不同
5. **滑点**：高杠杆交易可能面临更大的滑点风险

## 最佳实践

1. **从低杠杆开始**：新手建议从2倍杠杆开始
2. **严格止损**：设置合理的止损点
3. **分散投资**：不要将所有资金用于单一品种
4. **回测验证**：充分回测验证策略的有效性
5. **风险评估**：定期评估和调整风险敞口

## 总结

杠杆交易功能为BarGpt4回测系统增加了更真实的期货交易模拟能力。通过合理使用杠杆，可以提高资金使用效率，但同时也要注意风险控制。建议用户在使用杠杆功能时，充分理解其风险特征，并采取适当的风险管理措施。
