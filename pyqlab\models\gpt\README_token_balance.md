# Token分布不平衡问题解决方案

## 问题描述

在训练K线数据的GPT模型时，经常会遇到某些token（如token 795）占比过高的问题，导致：
- 模型总是预测高频token
- 预测多样性差
- 模型收敛困难
- 泛化能力弱

## 解决方案概览

本解决方案提供了一套完整的工具来诊断和解决token分布不平衡问题：

### 1. 核心工具文件

- `token_balance_utils.py` - Token平衡处理工具类
- `bar_gpt4.py` - 增强的BarGpt4模型（支持平衡损失）
- `analyze_token_imbalance.py` - Token分布分析脚本
- `train_balanced_bar_gpt4.py` - 平衡训练脚本
- `example_fix_token_imbalance.py` - 使用示例

### 2. 主要功能

#### 📊 分布分析
- Token频率统计
- 基尼系数计算
- 词汇表利用率分析
- 可视化分布图表

#### ⚖️ 平衡方法
- **Focal Loss** - 自动处理类别不平衡
- **加权交叉熵** - 基于类别权重的损失
- **多样性正则化** - 鼓励预测多样性
- **数据重采样** - 平衡训练数据分布

#### 🎯 训练优化
- 自适应学习率调整
- 梯度累积
- 早停机制
- 预测多样性监控

## 快速开始

### 步骤1: 分析现有数据

```bash
python analyze_token_imbalance.py \
    --data_path /path/to/your/training/data \
    --tokenizer_path /path/to/your/tokenizer \
    --vocab_size 1024 \
    --threshold 0.1 \
    --save_plots \
    --output_dir ./analysis_results
```

这将生成：
- Token分布分析报告
- 可视化图表
- 自动配置文件
- 解决方案建议

### 步骤2: 查看分析结果

分析完成后，检查输出目录中的文件：

```
analysis_results/
├── token_distribution.png      # 分布可视化
├── analysis_results.txt        # 详细分析报告
└── balanced_config.py          # 自动生成的配置
```

### 步骤3: 应用解决方案

根据分析结果，选择合适的解决方案：

#### 方案A: 轻度不平衡（基尼系数 < 0.6）
```python
# 使用标签平滑和轻微正则化
model = BarGpt4(
    vocab_size=1024,
    # ... 其他参数
)

# 训练时使用标签平滑
loss = F.cross_entropy(logits, targets, label_smoothing=0.05)
```

#### 方案B: 中度不平衡（基尼系数 0.6-0.8）
```python
# 使用Focal Loss
from pyqlab.models.gpt.bar_gpt4 import BarGpt4

model = BarGpt4(vocab_size=1024, ...)

# 训练时传入class_weights
class_weights = compute_class_weights(token_data)
logits, loss = model(code, x, x_mark, targets, class_weights=class_weights)
```

#### 方案C: 严重不平衡（基尼系数 > 0.8）
```bash
# 使用完整的平衡训练流程
python train_balanced_bar_gpt4.py \
    --use_class_weights \
    --balance_method focal_loss \
    --data_balance_method weighted_sampling \
    --learning_rate 3e-5 \
    --max_epochs 150
```

## 详细使用指南

### 1. TokenBalancer类使用

```python
from pyqlab.models.gpt.token_balance_utils import TokenBalancer

# 创建分析器
balancer = TokenBalancer(vocab_size=1024)

# 分析token分布
stats = balancer.analyze_token_distribution(
    token_sequences,
    save_plot=True,
    plot_path="distribution.png"
)

# 计算类别权重
class_weights = balancer.compute_class_weights(
    method='sqrt_inverse_freq',  # 'inverse_freq', 'log_inverse_freq'
    smooth_factor=1.0
)

# 数据重采样
resampled_data = balancer.resample_sequences(
    token_sequences,
    target_distribution='uniform',  # 'sqrt_uniform'
    max_samples_per_class=1000
)
```

### 2. 增强的BarGpt4模型

```python
from pyqlab.models.gpt.bar_gpt4 import BarGpt4

model = BarGpt4(
    block_size=128,
    vocab_size=1024,
    n_layer=6,
    n_head=8,
    d_model=512,
    # ... 其他参数
)

# 训练时使用平衡损失
logits, loss = model(
    code, x, x_mark, targets,
    class_weights=class_weights  # 可选的类别权重
)
```

### 3. 平衡训练配置

```python
# 自动生成的配置示例
LOSS_CONFIG = {
    'use_focal_loss': True,
    'focal_alpha': 0.25,
    'focal_gamma': 2.0,
    'use_class_weights': True,
    'weight_method': 'sqrt_inverse_freq',
    'label_smoothing': 0.1,
    'diversity_weight': 0.2,
}

TRAINING_CONFIG = {
    'learning_rate': 3e-5,
    'weight_decay': 0.1,
    'batch_size': 32,
    'max_epochs': 150,
    'gradient_clip_val': 1.0,
}
```

## 监控指标

训练过程中重点监控以下指标：

### 1. 预测多样性
```python
# 计算预测的唯一token数量
unique_predictions = len(set(predictions))
diversity_ratio = unique_predictions / vocab_size
```

### 2. Token频率分布
```python
# 统计预测中各token的频率
pred_counter = Counter(predictions)
max_frequency = max(pred_counter.values()) / len(predictions)
```

### 3. 熵值
```python
# 计算预测分布的熵
probs = F.softmax(logits, dim=-1)
entropy = -torch.sum(probs * torch.log(probs + 1e-8), dim=-1).mean()
```

## 常见问题解答

### Q1: 如何判断token分布是否不平衡？
A: 主要看以下指标：
- 基尼系数 > 0.6 表示中度不平衡
- 单个token频率 > 20% 表示严重不平衡
- 词汇表利用率 < 30% 表示多样性不足

### Q2: 哪种平衡方法效果最好？
A: 根据不平衡程度选择：
- 轻度：标签平滑 + 正则化
- 中度：Focal Loss + 类别权重
- 严重：组合方法（Focal Loss + 重采样 + 多样性正则化）

### Q3: 如何验证解决方案的效果？
A: 对比以下指标：
- 验证集预测多样性提升
- 高频token预测频率下降
- 模型收敛更稳定
- 整体性能不下降

### Q4: 训练时间会增加多少？
A: 通常增加10-20%：
- Focal Loss计算稍复杂
- 类别权重需要额外计算
- 多样性正则化增加计算量

## 最佳实践

1. **先分析再行动** - 使用分析工具了解问题严重程度
2. **渐进式解决** - 从轻微方法开始，逐步加强
3. **监控训练过程** - 实时观察预测多样性变化
4. **保存检查点** - 便于回滚到最佳状态
5. **验证泛化能力** - 确保解决方案不影响模型性能

## 示例运行

```bash
# 1. 运行完整示例
python example_fix_token_imbalance.py

# 2. 分析实际数据
python analyze_token_imbalance.py --data_path ./data --vocab_size 1024

# 3. 平衡训练
python train_balanced_bar_gpt4.py --use_class_weights --balance_method focal_loss
```

## 技术支持

如果遇到问题，请检查：
1. 数据格式是否正确
2. 依赖包是否安装完整
3. GPU内存是否充足
4. 配置参数是否合理

更多技术细节请参考源代码注释和示例。
