"""
Token平衡处理工具
解决训练数据中token分布不均衡的问题
"""

import torch
import numpy as np
from collections import Counter
from typing import List, Dict, Tuple, Optional
import matplotlib.pyplot as plt
import seaborn as sns


class TokenBalancer:
    """Token平衡处理器"""
    
    def __init__(self, vocab_size: int):
        self.vocab_size = vocab_size
        self.token_counts = None
        self.class_weights = None
        
    def analyze_token_distribution(self, token_sequences: List[List[int]], 
                                 save_plot: bool = True, 
                                 plot_path: str = "token_distribution.png") -> Dict:
        """
        分析token分布
        
        参数:
        - token_sequences: token序列列表
        - save_plot: 是否保存分布图
        - plot_path: 图片保存路径
        
        返回:
        - 分布统计信息
        """
        # 统计所有token
        all_tokens = []
        for seq in token_sequences:
            all_tokens.extend(seq)
        
        # 计算分布
        token_counter = Counter(all_tokens)
        self.token_counts = token_counter
        
        # 统计信息
        total_tokens = len(all_tokens)
        unique_tokens = len(token_counter)
        vocab_utilization = unique_tokens / self.vocab_size
        
        # 找出高频token
        most_common = token_counter.most_common(10)
        high_freq_tokens = []
        
        for token_id, count in most_common:
            percentage = count / total_tokens
            if percentage > 0.05:  # 超过5%认为是高频
                high_freq_tokens.append((token_id, count, percentage))
        
        print(f"=== Token分布分析 ===")
        print(f"总token数: {total_tokens:,}")
        print(f"唯一token数: {unique_tokens}")
        print(f"词汇表利用率: {vocab_utilization:.2%}")
        print(f"高频token数量: {len(high_freq_tokens)}")
        
        print(f"\n最常见的tokens:")
        for token_id, count, percentage in high_freq_tokens:
            print(f"  Token {token_id}: {count:,}次 ({percentage:.2%})")
            if percentage > 0.2:
                print(f"    ⚠️  警告: Token {token_id} 出现频率过高！")
        
        # 绘制分布图
        if save_plot:
            self._plot_distribution(token_counter, plot_path)
        
        return {
            'total_tokens': total_tokens,
            'unique_tokens': unique_tokens,
            'vocab_utilization': vocab_utilization,
            'high_freq_tokens': high_freq_tokens,
            'token_counts': dict(token_counter)
        }
    
    def compute_class_weights(self, method: str = 'inverse_freq', 
                            smooth_factor: float = 1.0) -> torch.Tensor:
        """
        计算类别权重
        
        参数:
        - method: 权重计算方法 ('inverse_freq', 'sqrt_inverse_freq', 'log_inverse_freq')
        - smooth_factor: 平滑因子
        
        返回:
        - 类别权重tensor
        """
        if self.token_counts is None:
            raise ValueError("请先调用analyze_token_distribution方法")
        
        # 创建权重数组
        weights = torch.ones(self.vocab_size)
        total_samples = sum(self.token_counts.values())
        
        for token_id, count in self.token_counts.items():
            if method == 'inverse_freq':
                # 逆频率权重
                weights[token_id] = total_samples / (count + smooth_factor)
            elif method == 'sqrt_inverse_freq':
                # 平方根逆频率权重（更温和）
                weights[token_id] = np.sqrt(total_samples / (count + smooth_factor))
            elif method == 'log_inverse_freq':
                # 对数逆频率权重（最温和）
                weights[token_id] = np.log(total_samples / (count + smooth_factor) + 1)
            else:
                raise ValueError(f"未知的权重计算方法: {method}")
        
        # 归一化权重
        weights = weights / weights.mean()
        
        self.class_weights = weights
        
        print(f"\n=== 类别权重计算 ===")
        print(f"方法: {method}")
        print(f"平滑因子: {smooth_factor}")
        print(f"权重范围: [{weights.min():.4f}, {weights.max():.4f}]")
        
        # 显示高频token的权重
        if self.token_counts:
            most_common = Counter(self.token_counts).most_common(5)
            print(f"高频token权重:")
            for token_id, count in most_common:
                print(f"  Token {token_id}: 权重={weights[token_id]:.4f}")
        
        return weights
    
    def resample_sequences(self, token_sequences: List[List[int]], 
                          target_distribution: str = 'uniform',
                          max_samples_per_class: int = None) -> List[List[int]]:
        """
        重采样token序列以平衡分布
        
        参数:
        - token_sequences: 原始token序列
        - target_distribution: 目标分布 ('uniform', 'sqrt_uniform')
        - max_samples_per_class: 每个类别的最大样本数
        
        返回:
        - 重采样后的序列
        """
        if self.token_counts is None:
            raise ValueError("请先调用analyze_token_distribution方法")
        
        # 按最后一个token分组序列（假设最后一个token是预测目标）
        sequences_by_target = {}
        for seq in token_sequences:
            if len(seq) > 0:
                target_token = seq[-1]
                if target_token not in sequences_by_target:
                    sequences_by_target[target_token] = []
                sequences_by_target[target_token].append(seq)
        
        # 计算目标样本数
        if target_distribution == 'uniform':
            # 均匀分布：每个类别相同数量
            if max_samples_per_class is None:
                target_count = min(len(seqs) for seqs in sequences_by_target.values())
            else:
                target_count = max_samples_per_class
        elif target_distribution == 'sqrt_uniform':
            # 平方根均匀分布：减少差异但不完全均匀
            max_count = max(len(seqs) for seqs in sequences_by_target.values())
            target_count = int(np.sqrt(max_count))
        else:
            raise ValueError(f"未知的目标分布: {target_distribution}")
        
        # 重采样
        resampled_sequences = []
        for token_id, seqs in sequences_by_target.items():
            if len(seqs) >= target_count:
                # 下采样
                sampled = np.random.choice(len(seqs), target_count, replace=False)
                resampled_sequences.extend([seqs[i] for i in sampled])
            else:
                # 上采样
                sampled = np.random.choice(len(seqs), target_count, replace=True)
                resampled_sequences.extend([seqs[i] for i in sampled])
        
        print(f"\n=== 重采样结果 ===")
        print(f"原始序列数: {len(token_sequences)}")
        print(f"重采样后序列数: {len(resampled_sequences)}")
        print(f"目标分布: {target_distribution}")
        print(f"每类目标样本数: {target_count}")
        
        return resampled_sequences
    
    def _plot_distribution(self, token_counter: Counter, save_path: str):
        """绘制token分布图"""
        # 获取前50个最常见的token
        top_tokens = token_counter.most_common(50)
        tokens, counts = zip(*top_tokens)
        
        plt.figure(figsize=(15, 8))
        
        # 子图1: 条形图
        plt.subplot(2, 1, 1)
        bars = plt.bar(range(len(tokens)), counts)
        plt.xlabel('Token排名')
        plt.ylabel('出现次数')
        plt.title('Top 50 Token分布')
        plt.xticks(range(0, len(tokens), 5), [f'T{tokens[i]}' for i in range(0, len(tokens), 5)], rotation=45)
        
        # 标记高频token
        total_tokens = sum(token_counter.values())
        for i, (token_id, count) in enumerate(top_tokens[:10]):
            percentage = count / total_tokens
            if percentage > 0.1:
                bars[i].set_color('red')
                plt.text(i, count, f'{percentage:.1%}', ha='center', va='bottom')
        
        # 子图2: 累积分布
        plt.subplot(2, 1, 2)
        cumulative_counts = np.cumsum(counts)
        cumulative_percentages = cumulative_counts / total_tokens * 100
        plt.plot(range(len(tokens)), cumulative_percentages, 'b-', linewidth=2)
        plt.xlabel('Token排名')
        plt.ylabel('累积百分比 (%)')
        plt.title('Token累积分布')
        plt.grid(True, alpha=0.3)
        
        # 添加参考线
        plt.axhline(y=80, color='r', linestyle='--', alpha=0.7, label='80%线')
        plt.axhline(y=90, color='orange', linestyle='--', alpha=0.7, label='90%线')
        plt.legend()
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"分布图已保存到: {save_path}")


def create_balanced_dataloader(dataset, tokenizer, batch_size: int = 32, 
                             balance_method: str = 'weighted_sampling') -> torch.utils.data.DataLoader:
    """
    创建平衡的数据加载器
    
    参数:
    - dataset: 数据集
    - tokenizer: tokenizer实例
    - batch_size: 批次大小
    - balance_method: 平衡方法 ('weighted_sampling', 'oversampling')
    
    返回:
    - 平衡的DataLoader
    """
    if balance_method == 'weighted_sampling':
        # 计算样本权重
        sample_weights = []
        for i in range(len(dataset)):
            sample = dataset[i]
            # 假设最后一个token是目标
            target_token = sample['targets'][-1] if 'targets' in sample else sample['x'][-1]
            # 使用逆频率作为权重
            weight = 1.0 / (tokenizer.token_counts.get(target_token.item(), 1) + 1)
            sample_weights.append(weight)
        
        # 创建加权采样器
        sampler = torch.utils.data.WeightedRandomSampler(
            weights=sample_weights,
            num_samples=len(dataset),
            replacement=True
        )
        
        return torch.utils.data.DataLoader(
            dataset, 
            batch_size=batch_size, 
            sampler=sampler,
            num_workers=4,
            pin_memory=True
        )
    
    else:
        # 标准DataLoader
        return torch.utils.data.DataLoader(
            dataset, 
            batch_size=batch_size, 
            shuffle=True,
            num_workers=4,
            pin_memory=True
        )


def analyze_model_predictions(model, dataloader, device: str = 'cuda') -> Dict:
    """
    分析模型预测的多样性
    
    参数:
    - model: 训练好的模型
    - dataloader: 数据加载器
    - device: 设备
    
    返回:
    - 预测分析结果
    """
    model.eval()
    all_predictions = []
    all_targets = []
    
    with torch.no_grad():
        for batch in dataloader:
            if isinstance(batch, dict):
                code = batch['code'].to(device)
                x = batch['x'].to(device)
                x_mark = batch['x_mark'].to(device)
                targets = batch['targets'].to(device)
            else:
                code, x, x_mark, targets = [item.to(device) for item in batch]
            
            logits, _ = model(code, x, x_mark)
            predictions = logits.argmax(dim=-1)
            
            all_predictions.extend(predictions[:, -1].cpu().tolist())  # 最后一个时间步
            all_targets.extend(targets[:, -1].cpu().tolist())
    
    # 分析预测多样性
    pred_counter = Counter(all_predictions)
    target_counter = Counter(all_targets)
    
    print(f"\n=== 模型预测分析 ===")
    print(f"预测样本数: {len(all_predictions)}")
    print(f"唯一预测数: {len(pred_counter)}")
    print(f"预测多样性: {len(pred_counter)/len(set(all_targets)):.2%}")
    
    print(f"\n最常见的预测:")
    for token_id, count in pred_counter.most_common(10):
        percentage = count / len(all_predictions)
        print(f"  Token {token_id}: {count}次 ({percentage:.2%})")
    
    return {
        'predictions': all_predictions,
        'targets': all_targets,
        'pred_diversity': len(pred_counter) / len(set(all_targets)),
        'pred_distribution': dict(pred_counter)
    }
