"""
测试修复后的BarGpt4模型
验证RoPE维度问题是否已解决
"""

import torch
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from pyqlab.models.gpt.bar_gpt4 import BarGpt4


def test_bar_gpt4_basic():
    """测试BarGpt4基本功能"""
    print("测试BarGpt4基本功能...")
    
    # 模型配置
    config = {
        'block_size': 64,
        'code_size': 100,
        'vocab_size': 1000,
        'n_layer': 2,
        'n_head': 8,  # 确保256能被8整除
        'd_model': 256,
        'time_encoding': 'timeF',
        'time_embed_type': 'time_feature',
        'freq': 't',
        'pos_embed_type': 'rope',
        'dropout': 0.1,
        'bias': True
    }
    
    # 创建模型
    model = BarGpt4(**config)
    model.eval()
    
    # 创建测试数据
    batch_size = 4
    seq_len = 32
    
    code = torch.randint(0, config['code_size'], (batch_size, seq_len))
    x = torch.randint(0, config['vocab_size'], (batch_size, seq_len))
    x_mark = torch.randn(batch_size, seq_len, 5)  # freq='t'需要5个时间特征
    targets = torch.randint(0, config['vocab_size'], (batch_size, seq_len))
    
    print(f"输入形状:")
    print(f"  code: {code.shape}")
    print(f"  x: {x.shape}")
    print(f"  x_mark: {x_mark.shape}")
    print(f"  targets: {targets.shape}")
    
    # 测试前向传播
    try:
        with torch.no_grad():
            logits, loss = model(code, x, x_mark, targets)
        
        print(f"\n前向传播成功!")
        print(f"  logits形状: {logits.shape}")
        print(f"  loss: {loss.item():.4f}")
        
        # 测试推理模式
        model.inference_mode()
        with torch.no_grad():
            logits_inference, loss_inference = model(code, x, x_mark)
        
        print(f"\n推理模式成功!")
        print(f"  推理logits形状: {logits_inference.shape}")
        print(f"  推理loss: {loss_inference}")
        
        return True
        
    except Exception as e:
        print(f"❌ 前向传播失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_rope_dimensions():
    """专门测试RoPE维度问题"""
    print("\n" + "="*50)
    print("测试RoPE维度兼容性...")
    
    # 测试不同的n_head和d_model组合
    test_configs = [
        {'n_head': 8, 'd_model': 256},   # 256/8=32 ✓
        {'n_head': 4, 'd_model': 256},   # 256/4=64 ✓
        {'n_head': 16, 'd_model': 256},  # 256/16=16 ✓
        {'n_head': 12, 'd_model': 768},  # 768/12=64 ✓
    ]
    
    for i, test_config in enumerate(test_configs):
        print(f"\n测试配置 {i+1}: n_head={test_config['n_head']}, d_model={test_config['d_model']}")
        
        config = {
            'block_size': 32,
            'code_size': 50,
            'vocab_size': 500,
            'n_layer': 1,
            'n_head': test_config['n_head'],
            'd_model': test_config['d_model'],
            'time_encoding': 'timeF',
            'time_embed_type': 'time_feature',
            'freq': 't',
            'pos_embed_type': 'rope',
            'dropout': 0.1,
            'bias': True
        }
        
        try:
            model = BarGpt4(**config)
            
            # 测试数据
            batch_size = 2
            seq_len = 16
            
            code = torch.randint(0, config['code_size'], (batch_size, seq_len))
            x = torch.randint(0, config['vocab_size'], (batch_size, seq_len))
            x_mark = torch.randn(batch_size, seq_len, 5)
            
            with torch.no_grad():
                logits, _ = model(code, x, x_mark)
            
            print(f"  ✅ 成功! head_dim={config['d_model']//config['n_head']}")
            
        except Exception as e:
            print(f"  ❌ 失败: {e}")


def test_token_balance_features():
    """测试token平衡功能"""
    print("\n" + "="*50)
    print("测试token平衡功能...")
    
    config = {
        'block_size': 32,
        'code_size': 50,
        'vocab_size': 100,
        'n_layer': 2,
        'n_head': 4,
        'd_model': 128,
        'time_encoding': 'timeF',
        'time_embed_type': 'time_feature',
        'freq': 't',
        'pos_embed_type': 'rope',
        'dropout': 0.1,
        'bias': True
    }
    
    model = BarGpt4(**config)
    model.train_mode()
    
    # 创建不平衡的测试数据（大部分是token 50）
    batch_size = 8
    seq_len = 16
    
    code = torch.randint(0, config['code_size'], (batch_size, seq_len))
    x = torch.randint(0, config['vocab_size'], (batch_size, seq_len))
    x_mark = torch.randn(batch_size, seq_len, 5)
    
    # 创建不平衡的targets（80%是token 50）
    targets = torch.full((batch_size, seq_len), 50)
    # 20%是其他token
    mask = torch.rand(batch_size, seq_len) < 0.2
    targets[mask] = torch.randint(0, config['vocab_size'], (mask.sum(),))
    
    print(f"目标token分布:")
    unique, counts = torch.unique(targets, return_counts=True)
    for token, count in zip(unique[:5], counts[:5]):  # 显示前5个
        percentage = count.item() / targets.numel() * 100
        print(f"  Token {token.item()}: {count.item()}次 ({percentage:.1f}%)")
    
    # 测试不同的损失计算方法
    try:
        # 1. 不使用类别权重（使用Focal Loss）
        with torch.no_grad():
            logits, loss_focal = model(code, x, x_mark, targets, class_weights=None)
        print(f"\nFocal Loss: {loss_focal.item():.4f}")
        
        # 2. 使用类别权重
        class_weights = torch.ones(config['vocab_size'])
        class_weights[50] = 0.2  # 降低高频token的权重
        
        with torch.no_grad():
            logits, loss_weighted = model(code, x, x_mark, targets, class_weights=class_weights)
        print(f"加权损失: {loss_weighted.item():.4f}")
        
        print("✅ Token平衡功能测试成功!")
        return True
        
    except Exception as e:
        print(f"❌ Token平衡功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("开始测试修复后的BarGpt4模型")
    print("="*60)
    
    success_count = 0
    total_tests = 3
    
    # 测试1: 基本功能
    if test_bar_gpt4_basic():
        success_count += 1
    
    # 测试2: RoPE维度
    test_rope_dimensions()
    success_count += 1  # 这个测试主要是展示，不计入失败
    
    # 测试3: Token平衡功能
    if test_token_balance_features():
        success_count += 1
    
    print("\n" + "="*60)
    print(f"测试完成: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！BarGpt4模型修复成功！")
        print("\n下一步建议:")
        print("1. 使用analyze_token_imbalance.py分析您的训练数据")
        print("2. 根据分析结果选择合适的平衡策略")
        print("3. 使用修复后的模型重新训练")
    else:
        print("⚠️  部分测试失败，请检查错误信息")


if __name__ == "__main__":
    main()
