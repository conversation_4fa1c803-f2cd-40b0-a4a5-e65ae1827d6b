"""
测试修复后的训练流程
验证RoPE维度问题是否已解决，能否正常开始训练
"""

import torch
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from pyqlab.models.pl_gpt_model import PLGptModel


def test_model_creation():
    """测试模型创建"""
    print("测试模型创建...")

    # 模拟训练脚本的参数
    class Args:
        def __init__(self):
            # 模型参数
            self.model_name = 'bar_gpt4'
            self.model_path = 'pyqlab.models.gpt'
            self.block_size = 64
            self.code_size = 96
            self.vocab_size = 1000  # 使用较小的词汇表进行测试
            self.n_layer = 2
            self.n_head = 8  # 确保256能被8整除
            self.d_model = 256
            self.bias = True
            self.dropout = 0.1
            self.time_encoding = 'timeF'
            self.freq = 't'
            self.time_embed_type = 'time_feature'
            self.pos_embed_type = 'rope'

            # 训练参数
            self.lr = 1e-3
            self.weight_decay = 0.0
            self.optimizer = 'adamw'
            self.lr_scheduler = 'reduce_on_plateau'
            self.lr_decay_steps = 5
            self.lr_decay_rate = 0.1
            self.lr_decay_min_lr = 1e-6

            # 其他参数
            self.loss = 'ce'
            self.version = 'GPT'

    args = Args()

    try:
        # 创建模型 - PLGptModel需要特定的参数格式
        model = PLGptModel(
            model_name=args.model_name,
            loss=args.loss,
            lr=args.lr,
            model_path=args.model_path,
            block_size=args.block_size,
            code_size=args.code_size,
            vocab_size=args.vocab_size,
            n_layer=args.n_layer,
            n_head=args.n_head,
            d_model=args.d_model,
            bias=args.bias,
            dropout=args.dropout,
            time_encoding=args.time_encoding,
            freq=args.freq,
            time_embed_type=args.time_embed_type,
            pos_embed_type=args.pos_embed_type,
            weight_decay=args.weight_decay,
            optimizer=args.optimizer,
            lr_scheduler=args.lr_scheduler,
            lr_decay_steps=args.lr_decay_steps,
            lr_decay_rate=args.lr_decay_rate,
            lr_decay_min_lr=args.lr_decay_min_lr,
            version=args.version
        )
        print(f"✅ 模型创建成功!")
        print(f"   模型类型: {type(model.model).__name__}")
        print(f"   参数数量: {model.model.get_num_params()/1e6:.2f}M")

        return model, args

    except Exception as e:
        print(f"❌ 模型创建失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None


def test_forward_pass(model, args):
    """测试前向传播"""
    print("\n测试前向传播...")

    if model is None:
        print("❌ 模型为空，跳过前向传播测试")
        return False

    try:
        # 创建测试数据
        batch_size = 4
        seq_len = 32

        code = torch.randint(0, args.code_size, (batch_size, seq_len))
        x = torch.randint(0, args.vocab_size, (batch_size, seq_len))
        x_mark = torch.randn(batch_size, seq_len, 5)  # freq='t'需要5个时间特征
        targets = torch.randint(0, args.vocab_size, (batch_size, seq_len))

        print(f"输入数据形状:")
        print(f"  code: {code.shape}")
        print(f"  x: {x.shape}")
        print(f"  x_mark: {x_mark.shape}")
        print(f"  targets: {targets.shape}")

        # 测试前向传播
        model.eval()
        with torch.no_grad():
            outputs, loss = model(code, x, x_mark, targets)

        print(f"\n前向传播成功!")
        print(f"  outputs形状: {outputs.shape}")
        print(f"  loss: {loss.item():.4f}")

        return True

    except Exception as e:
        print(f"❌ 前向传播失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_training_step(model, args):
    """测试训练步骤"""
    print("\n测试训练步骤...")

    if model is None:
        print("❌ 模型为空，跳过训练步骤测试")
        return False

    try:
        # 创建测试数据
        batch_size = 4
        seq_len = 32

        # PLGptModel期望batch是一个包含5个元素的元组
        batch = (
            torch.randint(0, args.code_size, (batch_size, seq_len)),  # code
            torch.randint(0, args.vocab_size, (batch_size, seq_len)),  # x
            torch.randn(batch_size, seq_len, 5),  # x_mark
            torch.randint(0, args.vocab_size, (batch_size, seq_len)),  # targets
            torch.randn(batch_size, seq_len, 3)  # y_mark (添加缺失的y_mark)
        )

        # 设置为训练模式
        model.train()

        # 测试训练步骤
        loss = model.training_step(batch, 0)

        print(f"训练步骤成功!")
        print(f"  训练loss: {loss.item():.4f}")

        # 测试验证步骤
        model.eval()
        with torch.no_grad():
            val_loss = model.validation_step(batch, 0)

        print(f"  验证loss: {val_loss.item():.4f}")

        return True

    except Exception as e:
        print(f"❌ 训练步骤失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_optimizer_configuration(model, args):
    """测试优化器配置"""
    print("\n测试优化器配置...")

    if model is None:
        print("❌ 模型为空，跳过优化器配置测试")
        return False

    try:
        # 配置优化器
        optimizer = model.configure_optimizers()

        print(f"优化器配置成功!")
        print(f"  优化器类型: {type(optimizer).__name__}")

        if hasattr(optimizer, 'param_groups'):
            print(f"  参数组数量: {len(optimizer.param_groups)}")
            for i, group in enumerate(optimizer.param_groups):
                print(f"    组{i}: {len(group['params'])}个参数, lr={group['lr']}, weight_decay={group['weight_decay']}")

        return True

    except Exception as e:
        print(f"❌ 优化器配置失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_different_configurations():
    """测试不同的模型配置"""
    print("\n" + "="*60)
    print("测试不同的模型配置...")

    configs = [
        {'n_head': 4, 'd_model': 256, 'name': '4头256维'},
        {'n_head': 8, 'd_model': 256, 'name': '8头256维'},
        {'n_head': 16, 'd_model': 256, 'name': '16头256维'},
        {'n_head': 8, 'd_model': 512, 'name': '8头512维'},
    ]

    success_count = 0

    for config in configs:
        print(f"\n测试配置: {config['name']}")

        class TestArgs:
            def __init__(self, n_head, d_model):
                self.model_name = 'bar_gpt4'
                self.model_path = 'pyqlab.models.gpt'
                self.block_size = 32
                self.code_size = 50
                self.vocab_size = 500
                self.n_layer = 2
                self.n_head = n_head
                self.d_model = d_model
                self.bias = True
                self.dropout = 0.1
                self.time_encoding = 'timeF'
                self.freq = 't'
                self.time_embed_type = 'time_feature'
                self.pos_embed_type = 'rope'
                self.lr = 1e-3
                self.weight_decay = 0.0
                self.optimizer = 'adamw'
                self.lr_scheduler = 'reduce_on_plateau'
                self.lr_decay_steps = 5
                self.lr_decay_rate = 0.1
                self.lr_decay_min_lr = 1e-6
                self.loss = 'ce'
                self.version = 'GPT'

        try:
            args = TestArgs(config['n_head'], config['d_model'])
            model = PLGptModel(
                model_name=args.model_name,
                loss=args.loss,
                lr=args.lr,
                model_path=args.model_path,
                block_size=args.block_size,
                code_size=args.code_size,
                vocab_size=args.vocab_size,
                n_layer=args.n_layer,
                n_head=args.n_head,
                d_model=args.d_model,
                bias=args.bias,
                dropout=args.dropout,
                time_encoding=args.time_encoding,
                freq=args.freq,
                time_embed_type=args.time_embed_type,
                pos_embed_type=args.pos_embed_type,
                weight_decay=args.weight_decay,
                optimizer=args.optimizer,
                lr_scheduler=args.lr_scheduler,
                lr_decay_steps=args.lr_decay_steps,
                lr_decay_rate=args.lr_decay_rate,
                lr_decay_min_lr=args.lr_decay_min_lr,
                version=args.version
            )

            # 简单的前向传播测试
            batch_size = 2
            seq_len = 16

            code = torch.randint(0, args.code_size, (batch_size, seq_len))
            x = torch.randint(0, args.vocab_size, (batch_size, seq_len))
            x_mark = torch.randn(batch_size, seq_len, 5)
            targets = torch.randint(0, args.vocab_size, (batch_size, seq_len))

            with torch.no_grad():
                outputs, loss = model(code, x, x_mark, targets)

            print(f"  ✅ 成功! head_dim={config['d_model']//config['n_head']}, loss={loss.item():.4f}")
            success_count += 1

        except Exception as e:
            print(f"  ❌ 失败: {e}")

    print(f"\n配置测试完成: {success_count}/{len(configs)} 成功")
    return success_count == len(configs)


def main():
    """主测试函数"""
    print("开始测试修复后的训练流程")
    print("="*60)

    success_count = 0
    total_tests = 5

    # 测试1: 模型创建
    model, args = test_model_creation()
    if model is not None:
        success_count += 1

    # 测试2: 前向传播
    if test_forward_pass(model, args):
        success_count += 1

    # 测试3: 训练步骤
    if test_training_step(model, args):
        success_count += 1

    # 测试4: 优化器配置
    if test_optimizer_configuration(model, args):
        success_count += 1

    # 测试5: 不同配置
    if test_different_configurations():
        success_count += 1

    print("\n" + "="*60)
    print(f"测试完成: {success_count}/{total_tests} 通过")

    if success_count == total_tests:
        print("🎉 所有测试通过！训练流程修复成功！")
        print("\n现在您可以安全地运行训练脚本:")
        print("cd pyqlab\\pl")
        print("python train_bar_gpt.py --max_epochs 1 --vocab_size 1000")
    else:
        print("⚠️  部分测试失败，请检查错误信息")


if __name__ == "__main__":
    main()
