"""
回测BarGpt4模型示例

演示如何使用回测器对BarGpt4模型进行回测
"""

import os
import sys
import argparse
import pandas as pd
import torch
import matplotlib.pyplot as plt
from datetime import datetime
import numpy as np
from pyqlab.const import MODEL_FUT_CODES

# 尝试导入onnxruntime
try:
    import onnxruntime as ort
    ONNX_AVAILABLE = True
except ImportError:
    ONNX_AVAILABLE = False
    print("警告: onnxruntime未安装，无法加载ONNX模型。可以使用 'pip install onnxruntime' 安装。")

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))

# 导入模型和回测器
from pyqlab.models.gpt.bar_gpt4 import BarGpt4
from pyqlab.models.gpt.bar_gpt4_backtester import BarGpt4Backtester
from pyqlab.models.gpt.onnx_model_wrapper import OnnxModelWrapper

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='回测BarGpt4模型')

    # 数据参数
    parser.add_argument('--data_path', type=str, required=True, help='数据文件路径')
    parser.add_argument('--code_column', type=str, default='code', help='证券代码列名')
    parser.add_argument('--datetime_column', type=str, default='datetime', help='日期时间列名')
    parser.add_argument('--begin_date', type=str, default=None, help='开始日期')
    parser.add_argument('--end_date', type=str, default=None, help='结束日期')

    # 模型参数
    parser.add_argument('--model_path', type=str, required=True, help='模型检查点路径')
    parser.add_argument('--time_encoding', type=str, default='timeF', help='时间编码类型')
    parser.add_argument('--time_embed_type', type=str, default='periodic', help='时间编码嵌入方法')
    parser.add_argument('--pos_embed_type', type=str, default='rope', help='位置编码类型')

    # 回测参数
    parser.add_argument('--initial_capital', type=float, default=10000.0, help='初始资金')
    parser.add_argument('--seq_len', type=int, default=30, help='序列长度')
    parser.add_argument('--commission', type=float, default=0.001, help='交易手续费率')
    parser.add_argument('--threshold', type=float, default=0.6, help='交易信号阈值')
    parser.add_argument('--stop_loss', type=float, default=None, help='止损比例')
    parser.add_argument('--take_profit', type=float, default=None, help='止盈比例')
    parser.add_argument('--temperature', type=float, default=1.0, help='温度参数')
    parser.add_argument('--signal_type', type=str, default=None,
                        choices=['threshold', 'topk', 'momentum', 'ensemble'],
                        help='信号生成器类型')
    parser.add_argument('--print_interval', type=int, default=10, help='打印详细信息的间隔步数')
    parser.add_argument('--leverage', type=float, default=1.0, help='交易杠杆倍数，默认1.0（无杠杆）')

    # 输出参数
    parser.add_argument('--output_dir', type=str, default='./results', help='输出目录')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')

    return parser.parse_args()

def set_seed(seed):
    """设置随机种子"""
    import random
    import numpy as np
    import torch

    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

def load_data(args):
    """加载数据"""
    print(f"加载数据: {args.data_path}")

    # 检查文件是否存在
    if not os.path.exists(args.data_path):
        # 如果文件不存在，创建一个测试数据集
        print(f"文件 {args.data_path} 不存在，创建测试数据集")

        # 创建测试数据
        np.random.seed(42)
        n_samples = 1000

        # 生成模拟的K线数据
        dates = pd.date_range(start='2023-01-01', periods=n_samples, freq='1min')

        # 生成价格数据（随机游走）
        initial_price = 5000
        returns = np.random.normal(0, 0.02, n_samples)
        prices = [initial_price]
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))

        # 生成OHLC数据
        df_data = []
        for i, (date, close) in enumerate(zip(dates, prices)):
            # 生成开盘价（基于前一个收盘价）
            if i == 0:
                open_price = close
            else:
                open_price = prices[i-1] * (1 + np.random.normal(0, 0.005))

            # 生成高低价
            high = max(open_price, close) * (1 + abs(np.random.normal(0, 0.01)))
            low = min(open_price, close) * (1 - abs(np.random.normal(0, 0.01)))

            # 生成成交量
            volume = np.random.randint(1000, 10000)

            df_data.append({
                'datetime': date,
                'open': open_price,
                'high': high,
                'low': low,
                'close': close,
                'volume': volume
            })

        df = pd.DataFrame(df_data)

        # 保存测试数据
        if args.data_path.endswith('.csv'):
            df.to_csv(args.data_path, index=False)
            print(f"测试数据已保存到 {args.data_path}")
        elif args.data_path.endswith('.parquet'):
            df.to_parquet(args.data_path, index=False)
            print(f"测试数据已保存到 {args.data_path}")
        else:
            # 默认保存为CSV
            csv_path = args.data_path.replace('.parquet', '.csv')
            df.to_csv(csv_path, index=False)
            print(f"测试数据已保存到 {csv_path}")
    else:
        # 根据文件扩展名选择加载方法
        if args.data_path.endswith('.csv'):
            df = pd.read_csv(args.data_path)
        elif args.data_path.endswith('.parquet'):
            df = pd.read_parquet(args.data_path)
        else:
            raise ValueError(f"不支持的文件格式: {args.data_path}")

    if 'code' in df.columns:
        df = df.loc[df['code'] == df['code'].iloc[0]]
        code_id = hash(df['code'].iloc[0]) % 100  # 简单的哈希映射到0-99
    else:
        # 如果没有code列，使用默认的code_id
        code_id = 23  # 默认证券代码ID

    # 设置日期时间索引
    if args.datetime_column in df.columns:
        df[args.datetime_column] = pd.to_datetime(df[args.datetime_column])
        df.set_index(args.datetime_column, inplace=True)

    # 过滤日期范围
    if args.begin_date:
        df = df[df.index >= args.begin_date]
    if args.end_date:
        df = df[df.index <= args.end_date]

    print(f"使用证券代码: {code_id}")
    print(f"数据加载完成，共 {len(df)} 行")
    return df, code_id

def load_onnx_model(model_path):
    """加载ONNX模型"""
    if not ONNX_AVAILABLE:
        raise ImportError("onnxruntime未安装，无法加载ONNX模型")

    print(f"加载ONNX模型: {model_path}")

    # 配置ONNX运行时选项
    providers = ['CUDAExecutionProvider', 'CPUExecutionProvider'] if torch.cuda.is_available() else ['CPUExecutionProvider']
    session_options = ort.SessionOptions()
    session_options.graph_optimization_level = ort.GraphOptimizationLevel.ORT_ENABLE_ALL
    session_options.intra_op_num_threads = 4

    # 创建ONNX运行时会话
    try:
        session = ort.InferenceSession(
            model_path,
            providers=providers,
            sess_options=session_options
        )

        # 获取模型输入输出信息
        input_names = [input.name for input in session.get_inputs()]
        output_names = [output.name for output in session.get_outputs()]

        print(f"ONNX模型输入: {input_names}")
        print(f"ONNX模型输出: {output_names}")

        # 确定设备
        device = torch.device('cuda' if 'CUDAExecutionProvider' in session.get_providers() else 'cpu')
        print(f"使用设备: {device}")

        # 创建ONNX模型包装器
        model = OnnxModelWrapper(session, device=device)

        return model, device

    except Exception as e:
        print(f"加载ONNX模型失败: {str(e)}")
        raise

def load_model(args):
    """加载模型"""
    print(f"加载模型: {args.model_path}")

    # 检查模型文件是否存在
    if not os.path.exists(args.model_path):
        print(f"模型文件 {args.model_path} 不存在，创建测试模型")

        # 创建模型目录
        os.makedirs(os.path.dirname(args.model_path), exist_ok=True)

        # 创建一个简单的模型
        model = BarGpt4(
            block_size=args.seq_len,
            code_size=100,
            vocab_size=40000,
            n_layer=2,  # 使用较小的模型以便快速测试
            n_head=4,
            d_model=64,
            time_encoding=args.time_encoding,
            time_embed_type=args.time_embed_type,
            freq='t',
            pos_embed_type=args.pos_embed_type,
            dropout=0.1
        )

        # 创建一个简单的检查点
        checkpoint = {
            'model_state_dict': model.state_dict(),
            'config': {
                'block_size': args.seq_len,
                'code_size': 100,
                'vocab_size': 40000,
                'n_layer': 2,
                'n_head': 4,
                'd_model': 64,
                'time_encoding': args.time_encoding,
                'time_embed_type': args.time_embed_type,
                'freq': 't',
                'pos_embed_type': args.pos_embed_type,
                'dropout': 0.1
            }
        }

        # 保存模型
        torch.save(checkpoint, args.model_path)
        print(f"测试模型已保存到 {args.model_path}")

        # 移动到设备
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model = model.to(device)
        model.eval()

        print(f"模型参数数量: {model.get_num_params():,}")
        print(f"使用设备: {device}")

        return model, device

    # 根据文件扩展名判断模型类型
    if args.model_path.endswith('.onnx'):
        # 加载ONNX模型
        return load_onnx_model(args.model_path)

    # 加载PyTorch模型
    # 加载模型检查点
    checkpoint = torch.load(args.model_path, map_location='cpu')

    # 从检查点中获取模型配置
    if 'config' in checkpoint:
        config = checkpoint['config']

        # 创建模型
        model = BarGpt4(
            block_size=config.get('block_size', args.seq_len),
            code_size=config.get('code_size', 100),
            vocab_size=config.get('vocab_size', 40000),
            n_layer=config.get('n_layer', 8),
            n_head=config.get('n_head', 8),
            d_model=config.get('d_model', 128),
            time_encoding=config.get('time_encoding', args.time_encoding),
            time_embed_type=config.get('time_embed_type', args.time_embed_type),
            freq=config.get('freq', 't'),
            pos_embed_type=config.get('pos_embed_type', args.pos_embed_type),
            dropout=config.get('dropout', 0.1)
        )
    else:
        # 使用默认配置创建模型
        model = BarGpt4(
            block_size=args.seq_len,
            code_size=100,
            vocab_size=40000,
            n_layer=8,
            n_head=8,
            d_model=128,
            time_encoding=args.time_encoding,
            time_embed_type=args.time_embed_type,
            freq='t',
            pos_embed_type=args.pos_embed_type,
            dropout=0.1
        )

    # 加载模型权重
    model.load_state_dict(checkpoint['model_state_dict'])

    # 移动到GPU
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    model.eval()

    print(f"模型参数数量: {model.get_num_params():,}")
    print(f"使用设备: {device}")

    return model, device

def main():
    """主函数"""
    # 解析参数
    args = parse_args()

    # 设置随机种子
    set_seed(args.seed)

    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)

    # 加载数据
    df, code_id = load_data(args)

    # 加载模型
    model, device = load_model(args)

    # 创建回测器
    backtester = BarGpt4Backtester(
        model=model,
        initial_capital=args.initial_capital,
        device=device,
        signal_type=args.signal_type,
        leverage=args.leverage
    )

    # 进行回测
    print("\n开始回测...")
    results = backtester.backtest(
        df=df,
        seq_len=args.seq_len,
        commission=args.commission,
        threshold=args.threshold,
        stop_loss=args.stop_loss,
        take_profit=args.take_profit,
        temperature=args.temperature,
        print_interval=args.print_interval
    )

    # 可视化回测结果
    print("\n可视化回测结果...")
    backtester.visualize_backtest(
        df=df,
        results=results,
        seq_len=args.seq_len,
        save_path=os.path.join(args.output_dir, 'backtest_chart.png')
    )

    # 保存回测结果
    results_path = os.path.join(args.output_dir, 'backtest_results.json')
    backtester.save_results(results, results_path)

    print(f"\n回测完成，结果已保存到 {args.output_dir}")

if __name__ == "__main__":
    main()
