"""
Token分布不平衡分析和解决方案脚本
专门用于分析和解决token 795等高频token问题
"""

import os
import sys
import torch
import argparse
import numpy as np
import pandas as pd
from pathlib import Path
from collections import Counter
import matplotlib.pyplot as plt
import seaborn as sns

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from pyqlab.models.gpt.token_balance_utils import TokenBalancer
from pyqlab.models.gpt2.vq_tokenizer import CandlestickVQTokenizer


def load_training_data_tokens(data_path: str, tokenizer_path: str = None) -> list:
    """
    加载训练数据的token序列
    
    参数:
    - data_path: 数据路径
    - tokenizer_path: tokenizer路径
    
    返回:
    - token序列列表
    """
    print(f"正在加载训练数据: {data_path}")
    
    # 这里需要根据实际的数据格式来实现
    # 示例实现：
    token_sequences = []
    
    # 如果有保存的token文件，直接加载
    token_file = os.path.join(data_path, "tokens.npy")
    if os.path.exists(token_file):
        tokens = np.load(token_file, allow_pickle=True)
        if isinstance(tokens[0], (list, np.ndarray)):
            token_sequences = [seq.tolist() if isinstance(seq, np.ndarray) else seq for seq in tokens]
        else:
            # 如果是一维数组，按固定长度分割
            seq_len = 128  # 假设序列长度
            token_sequences = [tokens[i:i+seq_len].tolist() for i in range(0, len(tokens)-seq_len, seq_len)]
    
    # 如果没有token文件，需要从原始数据生成
    elif tokenizer_path and os.path.exists(tokenizer_path):
        print("从原始数据生成tokens...")
        # 加载tokenizer
        tokenizer = CandlestickVQTokenizer.load(tokenizer_path)
        
        # 加载原始K线数据并tokenize
        # 这里需要根据实际数据格式实现
        # data_files = glob.glob(os.path.join(data_path, "*.csv"))
        # for file_path in data_files:
        #     df = pd.read_csv(file_path)
        #     tokens = tokenizer.tokenize(df)
        #     token_sequences.append(tokens)
        
        pass  # 占位符
    
    else:
        raise ValueError("无法找到token数据或tokenizer")
    
    print(f"加载完成，共 {len(token_sequences)} 个序列")
    return token_sequences


def analyze_high_frequency_tokens(token_sequences: list, vocab_size: int, threshold: float = 0.1):
    """
    分析高频token问题
    
    参数:
    - token_sequences: token序列列表
    - vocab_size: 词汇表大小
    - threshold: 高频阈值（占比）
    
    返回:
    - 分析结果
    """
    print(f"\n=== 高频Token分析 (阈值: {threshold:.1%}) ===")
    
    # 统计所有token
    all_tokens = []
    for seq in token_sequences:
        all_tokens.extend(seq)
    
    total_tokens = len(all_tokens)
    token_counter = Counter(all_tokens)
    
    # 找出高频token
    high_freq_tokens = []
    for token_id, count in token_counter.items():
        frequency = count / total_tokens
        if frequency > threshold:
            high_freq_tokens.append({
                'token_id': token_id,
                'count': count,
                'frequency': frequency
            })
    
    # 按频率排序
    high_freq_tokens.sort(key=lambda x: x['frequency'], reverse=True)
    
    print(f"发现 {len(high_freq_tokens)} 个高频token:")
    for token_info in high_freq_tokens:
        print(f"  Token {token_info['token_id']}: {token_info['count']:,}次 ({token_info['frequency']:.2%})")
        
        if token_info['frequency'] > 0.3:
            print(f"    🚨 严重不平衡: 频率超过30%")
        elif token_info['frequency'] > 0.2:
            print(f"    ⚠️  中度不平衡: 频率超过20%")
        elif token_info['frequency'] > 0.1:
            print(f"    ⚡ 轻度不平衡: 频率超过10%")
    
    # 计算基尼系数（衡量不平衡程度）
    frequencies = [count/total_tokens for count in token_counter.values()]
    frequencies.sort()
    n = len(frequencies)
    gini = (2 * sum((i+1) * freq for i, freq in enumerate(frequencies))) / (n * sum(frequencies)) - (n+1) / n
    
    print(f"\n分布不平衡指标:")
    print(f"  基尼系数: {gini:.4f} (0=完全平衡, 1=完全不平衡)")
    print(f"  词汇表利用率: {len(token_counter)/vocab_size:.2%}")
    print(f"  Top-10 token占比: {sum(count for _, count in token_counter.most_common(10))/total_tokens:.2%}")
    
    return {
        'high_freq_tokens': high_freq_tokens,
        'gini_coefficient': gini,
        'vocab_utilization': len(token_counter) / vocab_size,
        'top10_ratio': sum(count for _, count in token_counter.most_common(10)) / total_tokens,
        'token_counter': token_counter
    }


def suggest_solutions(analysis_results: dict, vocab_size: int):
    """
    根据分析结果提供解决方案建议
    
    参数:
    - analysis_results: 分析结果
    - vocab_size: 词汇表大小
    """
    print(f"\n=== 解决方案建议 ===")
    
    high_freq_tokens = analysis_results['high_freq_tokens']
    gini = analysis_results['gini_coefficient']
    vocab_util = analysis_results['vocab_utilization']
    
    solutions = []
    
    # 根据不平衡程度提供建议
    if gini > 0.8:
        solutions.append("🚨 严重不平衡，建议采用多种方法组合:")
        solutions.append("   1. 使用Focal Loss (gamma=2.0, alpha=0.25)")
        solutions.append("   2. 计算类别权重并应用到损失函数")
        solutions.append("   3. 数据重采样或增强")
        solutions.append("   4. 增加多样性正则化")
    elif gini > 0.6:
        solutions.append("⚠️  中度不平衡，建议:")
        solutions.append("   1. 使用Focal Loss或加权交叉熵")
        solutions.append("   2. 轻微的数据重采样")
        solutions.append("   3. 增加dropout和正则化")
    else:
        solutions.append("⚡ 轻度不平衡，建议:")
        solutions.append("   1. 使用标签平滑")
        solutions.append("   2. 调整学习率和训练策略")
    
    # 根据词汇表利用率提供建议
    if vocab_util < 0.3:
        solutions.append(f"\n📊 词汇表利用率过低 ({vocab_util:.1%}):")
        solutions.append("   1. 检查tokenizer训练是否充分")
        solutions.append("   2. 考虑减小词汇表大小")
        solutions.append("   3. 增加数据多样性")
    
    # 针对特定高频token的建议
    if len(high_freq_tokens) > 0:
        top_token = high_freq_tokens[0]
        if top_token['frequency'] > 0.5:
            solutions.append(f"\n🎯 Token {top_token['token_id']} 占比过高 ({top_token['frequency']:.1%}):")
            solutions.append("   1. 检查该token对应的K线模式")
            solutions.append("   2. 考虑细分该模式或调整向量化方法")
            solutions.append("   3. 在损失函数中特别处理该token")
    
    # 打印所有建议
    for solution in solutions:
        print(solution)
    
    # 提供具体的代码配置建议
    print(f"\n=== 具体配置建议 ===")
    print("1. 模型训练参数:")
    print("   - 使用Focal Loss: alpha=0.25, gamma=2.0")
    print("   - 多样性正则化权重: 0.1")
    print("   - 标签平滑: 0.05")
    
    print("\n2. 数据处理:")
    if gini > 0.7:
        print("   - 使用加权采样")
        print("   - 对高频token进行下采样")
    
    print("\n3. 训练策略:")
    print("   - 降低学习率: 1e-4 -> 5e-5")
    print("   - 增加训练轮数")
    print("   - 使用余弦退火学习率调度")


def generate_balanced_config(analysis_results: dict, save_path: str = "balanced_config.py"):
    """
    生成平衡训练的配置文件
    
    参数:
    - analysis_results: 分析结果
    - save_path: 配置文件保存路径
    """
    high_freq_tokens = analysis_results['high_freq_tokens']
    gini = analysis_results['gini_coefficient']
    
    config_content = f'''"""
自动生成的平衡训练配置
基于token分布分析结果
基尼系数: {gini:.4f}
高频token数量: {len(high_freq_tokens)}
"""

# 模型配置
MODEL_CONFIG = {{
    'block_size': 128,
    'vocab_size': 1024,
    'n_layer': 6,
    'n_head': 8,
    'd_model': 512,
    'dropout': 0.15,  # 增加dropout
}}

# 损失函数配置
LOSS_CONFIG = {{
    'use_focal_loss': {gini > 0.6},
    'focal_alpha': 0.25,
    'focal_gamma': {2.0 if gini > 0.7 else 1.5},
    'use_class_weights': {gini > 0.5},
    'weight_method': 'sqrt_inverse_freq',
    'label_smoothing': {0.1 if gini > 0.7 else 0.05},
    'diversity_weight': {0.2 if gini > 0.8 else 0.1},
}}

# 训练配置
TRAINING_CONFIG = {{
    'learning_rate': {3e-5 if gini > 0.7 else 5e-5},
    'weight_decay': 0.1,
    'batch_size': 32,
    'max_epochs': {150 if gini > 0.7 else 100},
    'gradient_clip_val': 1.0,
    'accumulate_grad_batches': {2 if gini > 0.8 else 1},
}}

# 数据平衡配置
DATA_BALANCE_CONFIG = {{
    'use_weighted_sampling': {gini > 0.6},
    'oversample_rare_tokens': {gini > 0.8},
    'undersample_common_tokens': {len(high_freq_tokens) > 0 and high_freq_tokens[0]['frequency'] > 0.3},
    'max_token_frequency': 0.2,  # 限制单个token最大频率
}}

# 高频token列表（需要特别处理）
HIGH_FREQ_TOKENS = {[token['token_id'] for token in high_freq_tokens[:5]]}
'''
    
    with open(save_path, 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print(f"平衡训练配置已保存到: {save_path}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='分析token分布不平衡问题')
    parser.add_argument('--data_path', type=str, required=True, help='训练数据路径')
    parser.add_argument('--tokenizer_path', type=str, help='Tokenizer路径')
    parser.add_argument('--vocab_size', type=int, default=1024, help='词汇表大小')
    parser.add_argument('--threshold', type=float, default=0.1, help='高频token阈值')
    parser.add_argument('--save_plots', action='store_true', help='保存分析图表')
    parser.add_argument('--output_dir', type=str, default='./token_analysis', help='输出目录')
    
    args = parser.parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    print("开始Token分布不平衡分析...")
    
    # 1. 加载数据
    try:
        token_sequences = load_training_data_tokens(args.data_path, args.tokenizer_path)
    except Exception as e:
        print(f"数据加载失败: {e}")
        print("请检查数据路径和格式")
        return
    
    # 2. 创建分析器
    balancer = TokenBalancer(args.vocab_size)
    
    # 3. 分析token分布
    distribution_stats = balancer.analyze_token_distribution(
        token_sequences, 
        save_plot=args.save_plots,
        plot_path=os.path.join(args.output_dir, "token_distribution.png")
    )
    
    # 4. 分析高频token问题
    analysis_results = analyze_high_frequency_tokens(
        token_sequences, 
        args.vocab_size, 
        args.threshold
    )
    
    # 5. 计算类别权重
    class_weights = balancer.compute_class_weights(method='sqrt_inverse_freq')
    
    # 6. 提供解决方案建议
    suggest_solutions(analysis_results, args.vocab_size)
    
    # 7. 生成平衡训练配置
    config_path = os.path.join(args.output_dir, "balanced_config.py")
    generate_balanced_config(analysis_results, config_path)
    
    # 8. 保存分析结果
    results_path = os.path.join(args.output_dir, "analysis_results.txt")
    with open(results_path, 'w', encoding='utf-8') as f:
        f.write("Token分布不平衡分析结果\\n")
        f.write("=" * 50 + "\\n")
        f.write(f"总序列数: {len(token_sequences)}\\n")
        f.write(f"词汇表大小: {args.vocab_size}\\n")
        f.write(f"词汇表利用率: {analysis_results['vocab_utilization']:.2%}\\n")
        f.write(f"基尼系数: {analysis_results['gini_coefficient']:.4f}\\n")
        f.write(f"Top-10占比: {analysis_results['top10_ratio']:.2%}\\n")
        f.write("\\n高频Token列表:\\n")
        for token_info in analysis_results['high_freq_tokens']:
            f.write(f"Token {token_info['token_id']}: {token_info['frequency']:.2%}\\n")
    
    print(f"\\n分析完成！结果已保存到: {args.output_dir}")
    print("\\n下一步建议:")
    print("1. 查看生成的配置文件并应用到训练中")
    print("2. 使用平衡训练脚本重新训练模型")
    print("3. 监控训练过程中的预测多样性")


if __name__ == "__main__":
    main()
